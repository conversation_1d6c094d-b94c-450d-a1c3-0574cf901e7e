import 'package:get/get.dart';
import 'package:novel_app/langchain/services/novel_generation_service.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/models/chat_message.dart';
import 'package:novel_app/services/novel_chat_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';

/// 小说生成控制器，处理与AI模型的交互
class NovelGenerationController extends GetxController {
  // 使用懒加载方式获取服务实例
  NovelGenerationService? _langchainServiceInstance;
  NovelChatService? _novelChatServiceInstance;
  ApiConfigController? _apiConfigControllerInstance;

  // 是否使用嵌入模型优化聊天
  final RxBool useEmbeddingForChat = true.obs;

  NovelGenerationService get _langchainService {
    try {
      _langchainServiceInstance ??= Get.find<NovelGenerationService>();
      return _langchainServiceInstance!;
    } catch (e) {
      print("[NovelGenerationController] 无法获取NovelGenerationService: $e");
      throw Exception("小说生成服务尚未初始化，请稍后再试");
    }
  }

  NovelChatService get _novelChatService {
    try {
      _novelChatServiceInstance ??= Get.find<NovelChatService>();
      return _novelChatServiceInstance!;
    } catch (e) {
      print("[NovelGenerationController] 无法获取NovelChatService: $e");
      throw Exception("小说聊天服务尚未初始化，请稍后再试");
    }
  }

  ApiConfigController get _apiConfigController {
    try {
      _apiConfigControllerInstance ??= Get.find<ApiConfigController>();
      return _apiConfigControllerInstance!;
    } catch (e) {
      print("[NovelGenerationController] 无法获取ApiConfigController: $e");
      throw Exception("API配置控制器尚未初始化，请稍后再试");
    }
  }

  @override
  void onInit() {
    super.onInit();
    // 监听嵌入模型启用状态
    _apiConfigController.embeddingModel.listen((model) {
      // 如果嵌入模型被禁用，则自动禁用聊天优化
      if (!model.enabled && useEmbeddingForChat.value) {
        useEmbeddingForChat.value = false;
      }
    });
  }

  /// 生成聊天回复
  ///
  /// [message] 用户消息
  /// [novelTitle] 小说标题
  Future<String> generateChatResponse(String message, String novelTitle) async {
    try {
      print("[NovelGenerationController] 为小说 '$novelTitle' 生成聊天回复");

      // 检查是否使用嵌入模型优化聊天
      final embeddingEnabled =
          _apiConfigController.embeddingModel.value.enabled;

      if (embeddingEnabled && useEmbeddingForChat.value) {
        print("[NovelGenerationController] 使用嵌入模型优化聊天");
        // 使用优化的聊天服务
        return await _novelChatService.generateChatResponse(
            message, novelTitle);
      } else {
        print("[NovelGenerationController] 使用传统方式生成聊天回复");

        // 获取聊天历史服务
        final chatHistoryService = Get.find<ChatHistoryService>();

        // 添加用户消息到历史记录
        final userMessage = ChatMessage.user(
          content: message,
          novelTitle: novelTitle,
        );
        await chatHistoryService.addMessage(userMessage);

        // 获取小说的NovelMemory实例
        final novelMemory = NovelMemory(novelTitle: novelTitle);

        // 获取小说的所有内容作为上下文
        final novelContent = await novelMemory.getAllNovelContent();

        // 调用LangChain服务生成回复
        final chain = await _langchainService.getChatChain(novelTitle);
        final response = await chain.run({
          'task': 'chat',
          'novelTitle': novelTitle,
          'userMessage': message,
          'novelContent': novelContent,
        });

        // 添加AI回复到历史记录
        final aiMessage = ChatMessage.ai(
          content: response,
          novelTitle: novelTitle,
        );
        await chatHistoryService.addMessage(aiMessage);

        print("[NovelGenerationController] 生成回复成功，长度: ${response.length}");
        return response;
      }
    } catch (e) {
      print("[NovelGenerationController] 生成回复失败: $e");
      return "抱歉，生成回复时出现错误: $e";
    }
  }

  /// 设置是否使用嵌入模型优化聊天
  void setUseEmbeddingForChat(bool value) {
    // 只有在嵌入模型启用时才能启用聊天优化
    if (value && !_apiConfigController.embeddingModel.value.enabled) {
      print("[NovelGenerationController] 嵌入模型未启用，无法启用聊天优化");
      return;
    }

    useEmbeddingForChat.value = value;
    print("[NovelGenerationController] 聊天优化状态: ${value ? '已启用' : '已禁用'}");
  }
}

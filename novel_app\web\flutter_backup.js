// 备用的Flutter初始化脚本
// 如果原始的flutter.js无法加载，可以尝试使用这个文件

(function() {
  // 定义flutter对象
  window.flutter = window.flutter || {};
  
  // 定义loader对象
  window.flutter.loader = {
    // 新的加载方法
    load: function(options) {
      console.log('Using backup Flutter loader');
      
      return new Promise(function(resolve, reject) {
        try {
          // 尝试加载main.dart.js
          var scriptTag = document.createElement('script');
          scriptTag.src = 'main.dart.js';
          scriptTag.type = 'application/javascript';
          
          scriptTag.onload = function() {
            console.log('main.dart.js loaded successfully');
            
            // 提供一个模拟的appRunner
            var mockAppRunner = {
              runApp: function() {
                console.log('Running app with backup loader');
                // 尝试调用实际的runApp函数
                if (typeof window.runApp === 'function') {
                  window.runApp();
                  return Promise.resolve();
                } else {
                  console.error('runApp function not found');
                  // 显示错误信息
                  document.querySelector('.loading').innerHTML = 
                    '<div style="text-align: center; font-family: sans-serif; color: #721c24; background-color: #f8d7da; padding: 20px; border-radius: 5px; max-width: 500px;">' +
                    '<h3>加载应用程序时出错</h3>' +
                    '<p>请尝试刷新页面或使用其他浏览器。</p>' +
                    '<p>如果问题持续存在，请联系管理员。</p>' +
                    '<button onclick="location.reload()" style="padding: 10px 20px; background: #0275d8; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 15px;">刷新页面</button>' +
                    '</div>';
                  return Promise.reject('runApp function not found');
                }
              }
            };
            
            resolve(mockAppRunner);
          };
          
          scriptTag.onerror = function(error) {
            console.error('Failed to load main.dart.js', error);
            // 显示错误信息
            document.querySelector('.loading').innerHTML = 
              '<div style="text-align: center; font-family: sans-serif; color: #721c24; background-color: #f8d7da; padding: 20px; border-radius: 5px; max-width: 500px;">' +
              '<h3>加载应用程序时出错</h3>' +
              '<p>请尝试刷新页面或使用其他浏览器。</p>' +
              '<p>如果问题持续存在，请联系管理员。</p>' +
              '<button onclick="location.reload()" style="padding: 10px 20px; background: #0275d8; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 15px;">刷新页面</button>' +
              '</div>';
            reject(error);
          };
          
          document.body.appendChild(scriptTag);
        } catch (error) {
          console.error('Error in backup loader:', error);
          reject(error);
        }
      });
    }
  };
})();

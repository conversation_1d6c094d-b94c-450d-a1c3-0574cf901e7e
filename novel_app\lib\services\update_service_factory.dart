import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/update_service.dart';
import 'package:novel_app/services/web_update_service.dart';

/// 更新服务工厂类
/// 根据平台创建不同的更新服务实例
class UpdateServiceFactory {
  /// 创建更新服务
  static GetxService createUpdateService() {
    if (kIsWeb) {
      // Web平台使用WebUpdateService
      return WebUpdateService();
    } else {
      // 其他平台使用原生UpdateService
      return UpdateService();
    }
  }
}

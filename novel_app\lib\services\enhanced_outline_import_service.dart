import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/novel_outline.dart';
import 'package:novel_app/services/ai_service.dart';

/// 增强版大纲导入服务
/// 提供使用AI和嵌入模型将用户输入的大纲转换为结构化JSON格式的功能
/// 支持逐章处理，显示进度，避免输入输出超出限制
class EnhancedOutlineImportService extends GetxService {
  final AIService _aiService = Get.find<AIService>();
  final ApiConfigController _apiConfigController =
      Get.find<ApiConfigController>();

  // 导入状态
  final RxBool isImporting = false.obs;
  final RxDouble importProgress = 0.0.obs;
  final RxString importStatus = ''.obs;
  final RxInt processedChapters = 0.obs;
  final RxInt totalChapters = 0.obs;

  /// 导入大纲并转换为JSON格式
  ///
  /// [outlineText] 用户输入的大纲文本
  /// [novelTitle] 小说标题
  /// [isDetailedOutline] 是否为细纲模式（true=细纲，false=普通大纲）
  /// [onProgress] 进度回调函数
  /// [onChapterProcessed] 章节处理完成回调函数
  Future<Map<String, dynamic>?> importOutline({
    required String outlineText,
    required String novelTitle,
    bool isDetailedOutline = false,
    void Function(String)? onProgress,
    void Function(int, int)? onChapterProcessed,
  }) async {
    if (isImporting.value) {
      // 已有导入任务在进行中
      return null;
    }

    try {
      isImporting.value = true;
      importProgress.value = 0.0;
      importStatus.value = '正在分析大纲结构...';
      processedChapters.value = 0;
      totalChapters.value = 0;

      if (onProgress != null) {
        onProgress('正在分析大纲结构...');
      }

      // 第一步：分析大纲结构，估算章节数量
      final chapterCount = _estimateChapterCount(outlineText);
      totalChapters.value = chapterCount;

      if (onProgress != null) {
        onProgress('检测到约 $chapterCount 个章节，开始处理...');
      }

      // 第二步：将大纲分割成章节
      final chapters = _splitOutlineIntoChapters(outlineText);
      totalChapters.value = chapters.length;

      if (onProgress != null) {
        onProgress('已分割为 ${chapters.length} 个章节，开始逐章转换...');
      }

      // 第三步：逐章处理，转换为JSON格式
      final List<Map<String, dynamic>> chaptersData = [];

      for (int i = 0; i < chapters.length; i++) {
        final chapterText = chapters[i];
        final chapterNumber = i + 1;

        importStatus.value = '正在处理第 $chapterNumber/${chapters.length} 章...';
        if (onProgress != null) {
          onProgress('正在处理第 $chapterNumber/${chapters.length} 章...');
        }

        // 使用AI将章节转换为JSON格式
        final chapterJson = await _convertChapterToJson(
          chapterText: chapterText,
          chapterNumber: chapterNumber,
          novelTitle: novelTitle,
          isDetailedOutline: isDetailedOutline,
        );

        if (chapterJson != null) {
          chaptersData.add(chapterJson);
        }

        processedChapters.value = i + 1;
        importProgress.value = (i + 1) / chapters.length;

        if (onChapterProcessed != null) {
          onChapterProcessed(i + 1, chapters.length);
        }
      }

      // 第四步：合并所有章节JSON，生成最终的大纲JSON
      final Map<String, dynamic> finalOutlineJson = {
        'title': novelTitle,
        'chapters': chaptersData,
      };

      importStatus.value = '导入完成！';
      importProgress.value = 1.0;

      if (onProgress != null) {
        onProgress('大纲导入完成！共处理 ${chaptersData.length} 章');
      }

      return finalOutlineJson;
    } catch (e) {
      importStatus.value = '导入失败: $e';
      return null;
    } finally {
      isImporting.value = false;
    }
  }

  /// 估算大纲中的章节数量
  int _estimateChapterCount(String outlineText) {
    // 使用正则表达式匹配常见的章节标记
    final chapterPatterns = [
      RegExp(r'第\s*(\d+)\s*章'),
      RegExp(r'Chapter\s*(\d+)'),
      RegExp(r'(\d+)[\.、]'),
    ];

    int maxCount = 0;

    for (final pattern in chapterPatterns) {
      final matches = pattern.allMatches(outlineText);
      if (matches.length > maxCount) {
        maxCount = matches.length;
      }
    }

    // 如果没有检测到章节标记，则按照段落估算
    if (maxCount == 0) {
      final paragraphs = outlineText.split('\n\n');
      return paragraphs.length;
    }

    return maxCount;
  }

  /// 将大纲分割成章节
  List<String> _splitOutlineIntoChapters(String outlineText) {
    // 使用正则表达式匹配常见的章节标记
    final chapterPatterns = [
      RegExp(r'第\s*\d+\s*章'),
      RegExp(r'Chapter\s*\d+'),
      RegExp(r'\d+[\.、]'),
    ];

    // 查找最匹配的模式
    RegExp? bestPattern;
    int maxMatches = 0;

    for (final pattern in chapterPatterns) {
      final matches = pattern.allMatches(outlineText);
      if (matches.length > maxMatches) {
        maxMatches = matches.length;
        bestPattern = pattern;
      }
    }

    // 如果找到了章节标记，按标记分割
    if (bestPattern != null && maxMatches > 0) {
      final matches = bestPattern.allMatches(outlineText).toList();
      final chapters = <String>[];

      for (int i = 0; i < matches.length; i++) {
        final start = matches[i].start;
        final end =
            i < matches.length - 1 ? matches[i + 1].start : outlineText.length;

        final chapterText = outlineText.substring(start, end).trim();
        if (chapterText.isNotEmpty) {
          chapters.add(chapterText);
        }
      }

      return chapters;
    }

    // 如果没有检测到章节标记，则按照段落分割
    final paragraphs = outlineText.split('\n\n');
    return paragraphs.where((p) => p.trim().isNotEmpty).toList();
  }

  /// 将章节文本转换为JSON格式
  Future<Map<String, dynamic>?> _convertChapterToJson({
    required String chapterText,
    required int chapterNumber,
    required String novelTitle,
    bool isDetailedOutline = false,
  }) async {
    try {
      final buffer = StringBuffer();

      // 构建系统提示词
      final String systemPrompt = isDetailedOutline
          ? '''
你是一位专业的小说细纲解析专家。
任务：将用户提供的小说章节细纲文本解析为结构化的JSON格式，保留所有细节。
'''
          : '''
你是一位专业的小说大纲解析专家。
任务：将用户提供的小说章节大纲文本解析为结构化的JSON格式。
''';

      // 构建用户提示词
      final userPrompt = isDetailedOutline
          ? '''
请将以下《$novelTitle》的第$chapterNumber章细纲内容解析为JSON格式。
应包含章节编号(chapterNumber)、章节标题(chapterTitle)和内容概要(summary)。
这是一个细纲，请保留所有情节细节和描述，不要简化或概括内容。

细纲内容：
$chapterText

请直接返回JSON对象，不要包含其他解释文字。格式如下：
{
  "chapterNumber": $chapterNumber,
  "chapterTitle": "章节标题",
  "summary": "完整的细纲内容，保留所有细节"
}
'''
          : '''
请将以下《$novelTitle》的第$chapterNumber章大纲内容解析为JSON格式。
应包含章节编号(chapterNumber)、章节标题(chapterTitle)和内容概要(summary)。

大纲内容：
$chapterText

请直接返回JSON对象，不要包含其他解释文字。格式如下：
{
  "chapterNumber": $chapterNumber,
  "chapterTitle": "章节标题",
  "summary": "详细内容概要"
}
''';

      // 使用AI服务生成内容
      await for (final chunk in _aiService.generateOutlineTextStream(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        temperature: 0.3,
        maxTokens: 2000,
        novelTitle: novelTitle,
        // 如果启用了嵌入模型，会在AIService内部自动使用
      )) {
        buffer.write(chunk);
      }

      final response = buffer.toString().trim();

      // 提取JSON
      final jsonResponse = _extractJson(response);
      if (jsonResponse == null) {
        // 无法从响应中提取JSON
        return {
          'chapterNumber': chapterNumber,
          'chapterTitle': '第$chapterNumber章',
          'summary': chapterText,
        };
      }

      // 确保JSON包含必要的字段
      if (!jsonResponse.containsKey('chapterNumber')) {
        jsonResponse['chapterNumber'] = chapterNumber;
      }

      if (!jsonResponse.containsKey('chapterTitle') ||
          jsonResponse['chapterTitle'] == null ||
          jsonResponse['chapterTitle'].isEmpty) {
        jsonResponse['chapterTitle'] = '第$chapterNumber章';
      }

      if (!jsonResponse.containsKey('summary') ||
          jsonResponse['summary'] == null ||
          jsonResponse['summary'].isEmpty) {
        jsonResponse['summary'] = chapterText;
      }

      return jsonResponse;
    } catch (e) {
      // 转换失败，返回一个基本的JSON对象
      return {
        'chapterNumber': chapterNumber,
        'chapterTitle': '第$chapterNumber章',
        'summary': chapterText,
      };
    }
  }

  /// 从文本中提取JSON
  Map<String, dynamic>? _extractJson(String text) {
    try {
      // 尝试直接解析整个文本
      return jsonDecode(text) as Map<String, dynamic>;
    } catch (e) {
      // 如果直接解析失败，尝试使用正则表达式提取JSON部分
      final jsonRegex = RegExp(r'\{[\s\S]*\}');
      final match = jsonRegex.firstMatch(text);

      if (match != null) {
        try {
          return jsonDecode(match.group(0)!) as Map<String, dynamic>;
        } catch (e) {
          // 提取的JSON解析失败
        }
      }

      return null;
    }
  }
}

/// 小说生成模式枚举
enum GenerationMode {
  /// 标准生成模式 - 保存所有章节内容和记忆
  standard,

  /// 精简生成模式 - 使用相同的大纲和细纲生成模块，但只保存细纲记忆
  lightweight,
}

/// 生成模式扩展方法
extension GenerationModeExtension on GenerationMode {
  /// 获取生成模式的显示名称
  String get displayName {
    switch (this) {
      case GenerationMode.standard:
        return '标准生成';
      case GenerationMode.lightweight:
        return '精简生成';
    }
  }

  /// 获取生成模式的描述
  String get description {
    switch (this) {
      case GenerationMode.standard:
        return '保存所有章节内容和记忆，生成质量更高，但消耗更多资源';
      case GenerationMode.lightweight:
        return '使用相同的大纲和细纲生成模块，但只保存细纲记忆，生成效率更高，消耗资源更少';
    }
  }
}

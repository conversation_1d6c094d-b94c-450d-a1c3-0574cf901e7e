import 'package:hive/hive.dart';
import 'package:novel_app/models/writing_style_package.dart';

class WritingStylePackageAdapter extends TypeAdapter<WritingStylePackage> {
  @override
  final int typeId = 5;

  @override
  WritingStylePackage read(BinaryReader reader) {
    return WritingStylePackage(
      id: reader.read(),
      name: reader.read(),
      description: reader.read(),
      sampleTexts: List<String>.from(reader.read()),
      author: reader.read(),
      createdAt: reader.read(),
      updatedAt: reader.read(),
    );
  }

  @override
  void write(BinaryWriter writer, WritingStylePackage obj) {
    writer.write(obj.id);
    writer.write(obj.name);
    writer.write(obj.description);
    writer.write(obj.sampleTexts);
    writer.write(obj.author);
    writer.write(obj.createdAt);
    writer.write(obj.updatedAt);
  }
} 
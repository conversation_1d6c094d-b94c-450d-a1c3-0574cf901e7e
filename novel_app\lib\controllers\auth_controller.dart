import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:novel_app/models/user.dart';
import 'package:novel_app/screens/home/<USER>';
import 'package:novel_app/screens/auth/login_screen.dart';

class AuthController extends GetxController {
  final _storage = GetStorage();

  // 用户状态
  final Rx<User?> currentUser = Rx<User?>(null);
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;

  // 存储键
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  // API基础URL
  String get _baseUrl => 'http://localhost:8000'; // 可以从配置中获取

  @override
  void onInit() {
    super.onInit();
    // 尝试从存储中恢复用户会话
    _loadUserSession();
  }

  // 加载用户会话
  void _loadUserSession() {
    final token = _storage.read(_tokenKey);
    final userData = _storage.read(_userKey);

    if (token != null && userData != null) {
      try {
        currentUser.value = User.fromJson(jsonDecode(userData));
        isLoggedIn.value = true;
      } catch (e) {
        print('恢复用户会话失败: $e');
        logout(); // 清除无效的会话数据
      }
    }
  }

  // 保存用户会话
  void _saveUserSession(String token, User user) {
    _storage.write(_tokenKey, token);
    _storage.write(_userKey, jsonEncode(user.toJson()));
    currentUser.value = user;
    isLoggedIn.value = true;
  }

  // 注册
  Future<void> register(String username, String email, String password) async {
    isLoading.value = true;

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/v1/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        Get.snackbar('成功', '注册成功，请登录');
        Get.off(() => LoginScreen());
      } else {
        final errorData = jsonDecode(response.body);
        Get.snackbar('注册失败', errorData['detail'] ?? '未知错误');
      }
    } catch (e) {
      Get.snackbar('错误', '注册请求失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 登录
  Future<void> login(String username, String password) async {
    isLoading.value = true;

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/v1/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final token = data['access_token'];
        final user = User.fromJson(data['user']);

        _saveUserSession(token, user);
        Get.offAll(() => const HomeScreen());
        Get.snackbar('成功', '登录成功');
      } else {
        final errorData = jsonDecode(response.body);
        Get.snackbar('登录失败', errorData['detail'] ?? '用户名或密码错误');
      }
    } catch (e) {
      Get.snackbar('错误', '登录请求失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 登出
  void logout() {
    _storage.remove(_tokenKey);
    _storage.remove(_userKey);
    currentUser.value = null;
    isLoggedIn.value = false;
    Get.offAll(() => LoginScreen());
  }

  // 获取当前用户信息
  Future<void> getCurrentUser() async {
    final token = _storage.read(_tokenKey);
    if (token == null) return;

    isLoading.value = true;

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/v1/auth/me'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        currentUser.value = User.fromJson(userData);
      } else {
        // 令牌可能已过期
        logout();
      }
    } catch (e) {
      print('获取用户信息失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 更新用户信息
  Future<void> updateUser({
    String? username,
    String? email,
    String? password,
  }) async {
    final token = _storage.read(_tokenKey);
    if (token == null) return;

    isLoading.value = true;

    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/api/v1/auth/me'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          if (username != null) 'username': username,
          if (email != null) 'email': email,
          if (password != null) 'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        currentUser.value = User.fromJson(userData);
        _storage.write(_userKey, jsonEncode(userData));
        Get.snackbar('成功', '用户信息已更新');
      } else {
        final errorData = jsonDecode(response.body);
        Get.snackbar('更新失败', errorData['detail'] ?? '未知错误');
      }
    } catch (e) {
      Get.snackbar('错误', '更新用户信息失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 获取认证头
  Map<String, String> get authHeaders {
    final token = _storage.read(_tokenKey);
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }
}

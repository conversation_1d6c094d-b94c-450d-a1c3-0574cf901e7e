import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/novel_generation_controller.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';

/// 聊天设置界面
/// 用于配置小说聊天的相关设置
class ChatSettingsScreen extends StatelessWidget {
  final String novelTitle;
  
  const ChatSettingsScreen({
    Key? key,
    required this.novelTitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final apiConfigController = Get.find<ApiConfigController>();
    final novelGenerationController = Get.find<NovelGenerationController>();
    final vectorizationService = Get.find<NovelVectorizationService>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('聊天设置'),
      ),
      body: Obx(() {
        final embeddingEnabled = apiConfigController.embeddingModel.value.enabled;
        final useEmbeddingForChat = novelGenerationController.useEmbeddingForChat.value;
        final isVectorized = vectorizationService.isNovelVectorized(novelTitle);
        final isVectorizing = vectorizationService.isVectorizing.value;
        final vectorizationProgress = vectorizationService.vectorizationProgress.value;
        final vectorizationStatus = vectorizationService.vectorizationStatus.value;
        
        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // 嵌入模型状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          embeddingEnabled ? Icons.check_circle : Icons.cancel,
                          color: embeddingEnabled ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          embeddingEnabled ? '嵌入模型已启用' : '嵌入模型未启用',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: embeddingEnabled ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      embeddingEnabled
                          ? '当前使用嵌入模型：${apiConfigController.embeddingModel.value.name}'
                          : '启用嵌入模型可以提高聊天质量，避免重复内容',
                      style: TextStyle(
                        fontSize: 14,
                        color: embeddingEnabled ? Colors.green.shade700 : Colors.red.shade700,
                      ),
                    ),
                    if (embeddingEnabled) ...[
                      const SizedBox(height: 16),
                      const Text(
                        '嵌入模型可以帮助AI更好地理解小说内容，提供更准确的回答。',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                    if (!embeddingEnabled) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          Get.toNamed('/api_config');
                        },
                        child: const Text('前往设置嵌入模型'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 聊天优化设置
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '聊天优化设置',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('使用嵌入模型优化聊天'),
                      subtitle: Text(
                        useEmbeddingForChat
                            ? '已启用：AI将根据问题智能检索相关小说内容'
                            : '未启用：AI将使用传统方式加载小说内容',
                      ),
                      value: useEmbeddingForChat,
                      onChanged: embeddingEnabled
                          ? (value) {
                              novelGenerationController.setUseEmbeddingForChat(value);
                            }
                          : null,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      useEmbeddingForChat
                          ? '优化后的聊天可以更准确地回答关于小说内容的问题，减少重复和错误'
                          : '传统方式可能会导致AI回答不够准确，特别是对于长篇小说',
                      style: TextStyle(
                        fontSize: 14,
                        color: useEmbeddingForChat ? Colors.green.shade700 : Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 小说向量化状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          isVectorized ? Icons.check_circle : Icons.pending,
                          color: isVectorized ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isVectorized ? '小说已向量化' : '小说未向量化',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isVectorized ? Colors.green : Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isVectorized
                          ? '小说《$novelTitle》已完成向量化，可以使用优化聊天功能'
                          : '小说《$novelTitle》尚未向量化，首次使用优化聊天时会自动进行向量化',
                      style: TextStyle(
                        fontSize: 14,
                        color: isVectorized ? Colors.green.shade700 : Colors.orange.shade700,
                      ),
                    ),
                    
                    if (isVectorizing) ...[
                      const SizedBox(height: 16),
                      Text(
                        '正在向量化: ${(vectorizationProgress * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: vectorizationProgress,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        vectorizationStatus,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                    
                    if (!isVectorized && !isVectorizing && embeddingEnabled) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          await vectorizationService.vectorizeNovel(novelTitle);
                        },
                        child: const Text('立即向量化'),
                      ),
                    ],
                    
                    if (isVectorized && !isVectorizing) ...[
                      const SizedBox(height: 16),
                      OutlinedButton(
                        onPressed: () async {
                          final confirmed = await Get.dialog<bool>(
                            AlertDialog(
                              title: const Text('确认重新向量化'),
                              content: const Text('重新向量化将删除现有的向量数据，并重新生成。确定要继续吗？'),
                              actions: [
                                TextButton(
                                  onPressed: () => Get.back(result: false),
                                  child: const Text('取消'),
                                ),
                                TextButton(
                                  onPressed: () => Get.back(result: true),
                                  child: const Text('确定'),
                                ),
                              ],
                            ),
                          );
                          
                          if (confirmed == true) {
                            await vectorizationService.clearNovelVectors(novelTitle);
                            await vectorizationService.vectorizeNovel(novelTitle);
                          }
                        },
                        child: const Text('重新向量化'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 说明
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '关于嵌入模型优化',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '嵌入模型优化是一种先进的技术，可以帮助AI更好地理解小说内容，提供更准确的回答。',
                      style: TextStyle(fontSize: 14),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '优化原理：',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '1. 将小说内容分段并转换为向量（向量化）',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      '2. 当您提问时，系统会找出与问题最相关的内容片段',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      '3. AI只需阅读相关内容，而非整本小说，从而提供更准确的回答',
                      style: TextStyle(fontSize: 14),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '优化效果：',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '• 更准确地回答关于小说情节、人物和背景的问题',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      '• 减少AI回答中的幻觉和错误',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      '• 提高长篇小说的聊天质量',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}

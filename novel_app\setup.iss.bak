#define MyAppName "DaiZhong Novel"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "DaiZhong"
#define MyAppExeName "novel_app.exe"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocExt ".novel"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; 注意: AppId的值为标识此应用程序的唯一值。
; 不要在其他安装程序中使用相同的AppId值。
AppId={{F8B0A845-5C7F-4FE3-A6B8-D42F03D7F0C1}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL=https://www.daizhong.com/
AppSupportURL=https://www.daizhong.com/
AppUpdatesURL=https://www.daizhong.com/

; 默认安装目录
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}

; 禁用欢迎页面上的选择目录按钮
DisableDirPage=no
DisableProgramGroupPage=yes

; 输出目录和文件名
OutputDir=D:\project\cuosor\novel_app\novel_app-1\build\windows\installer
OutputBaseFilename=DaiZhong_Novel_Setup

; 压缩设置
Compression=lzma
SolidCompression=yes

; 安装程序外观设置
WizardStyle=modern
SetupIconFile=D:\project\cuosor\novel_app\novel_app-1\windows\runner\resources\app_icon.ico

; 仅支持64位Windows
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; 主程序文件 - 修正文件路径
Source: "D:\project\cuosor\novel_app\novel_app-1\build\windows\x64\runner\Debug\novel_app.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\project\cuosor\novel_app\novel_app-1\build\windows\x64\runner\Debug\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; 包含VC++运行时库安装程序
#ifdef HAVE_REDIST
Source: "D:\project\cuosor\novel_app\novel_app-1\windows\installer\vc_redist.x64.exe"; DestDir: "{tmp}"; Flags: deleteafterinstall onlyifdoesntexist
#endif

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Registry]
; 文件关联设置
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".novel"; ValueData: ""

[Run]
#ifdef HAVE_REDIST
; 在需要时安装Visual C++运行时库
Filename: "{tmp}\vc_redist.x64.exe"; Parameters: "/install /quiet /norestart"; StatusMsg: "正在安装Visual C++运行时库..."; Flags: waituntilterminated skipifsilent; Check: NeedVCRedist
#endif

; 启动应用程序
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Messages]
SetupAppRunningError=安装程序检测到 %1 正在运行。%n%n请关闭所有实例，然后点击"确定"继续，或点击"取消"退出。

[Code]
var
  VCRedistNeedsInstallGlobal: Boolean;

// 检查是否需要安装完整的VC++运行时库
function NeedVCRedist: Boolean;
begin
  Result := VCRedistNeedsInstallGlobal;
end;

// 在安装前检查运行时库状态
function InitializeSetup(): Boolean;
var
  RegKey: String;
  VersionInstalled: Cardinal;
begin
  // 检查是否已安装VC++2015-2022 Redistributable (x64)
  RegKey := 'SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64';
  if not RegQueryDWordValue(HKEY_LOCAL_MACHINE, RegKey, 'Version', VersionInstalled) then
  begin
    VCRedistNeedsInstallGlobal := True;
  end else begin
    // 检查版本是否低于14.29.30135.0
    VCRedistNeedsInstallGlobal := VersionInstalled < 201033600; // 版本号的数值表示，可能需要根据实际情况调整
  end;
  
  // 检查是否存在VCRUNTIME140_1.dll
  if not VCRedistNeedsInstallGlobal then
  begin
    VCRedistNeedsInstallGlobal := not FileExists(ExpandConstant('{sys}\VCRUNTIME140_1.dll'));
  end;
  
  Result := True; // 继续安装
end; 
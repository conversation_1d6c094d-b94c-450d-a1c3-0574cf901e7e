// 基础API URL
const API_BASE_URL = '/api/v1';

// 创建Vue应用
const app = Vue.createApp({
    data() {
        return {
            // 认证相关
            isLoggedIn: false,
            loginForm: {
                username: '',
                password: ''
            },
            loginError: null,
            currentUser: null,
            token: null,
            
            // UI状态
            isLoading: false,
            currentPage: 'dashboard',
            
            // 数据
            announcements: [],
            users: [],
            stats: {
                userCount: 0,
                novelCount: 0,
                activeAnnouncementCount: 0
            },
            settings: {
                appName: '岱宗文脉',
                appVersion: '4.2.5',
                updateUrl: 'http://*************/api/version.json'
            },
            
            // 编辑状态
            editingAnnouncement: {
                id: null,
                title: '',
                content: '',
                is_important: false,
                is_active: true
            },
            editingUser: {
                id: null,
                username: '',
                email: '',
                password: '',
                is_vip: false,
                is_admin: false,
                is_active: true
            }
        }
    },
    
    mounted() {
        // 检查本地存储中是否有令牌
        const token = localStorage.getItem('admin_token');
        const userData = localStorage.getItem('admin_user');
        
        if (token && userData) {
            this.token = token;
            this.currentUser = JSON.parse(userData);
            this.isLoggedIn = true;
            
            // 设置axios默认头部
            axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
            
            // 加载初始数据
            this.loadDashboardData();
            this.loadAnnouncements();
            this.loadUsers();
        }
    },
    
    methods: {
        // 认证相关方法
        async login() {
            this.isLoading = true;
            this.loginError = null;
            
            try {
                const response = await axios.post(`${API_BASE_URL}/auth/login`, this.loginForm);
                
                // 检查用户是否是管理员
                if (!response.data.user.is_admin) {
                    this.loginError = '您没有管理员权限';
                    this.isLoading = false;
                    return;
                }
                
                this.token = response.data.access_token;
                this.currentUser = response.data.user;
                this.isLoggedIn = true;
                
                // 保存到本地存储
                localStorage.setItem('admin_token', this.token);
                localStorage.setItem('admin_user', JSON.stringify(this.currentUser));
                
                // 设置axios默认头部
                axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
                
                // 加载初始数据
                this.loadDashboardData();
                this.loadAnnouncements();
                this.loadUsers();
                
            } catch (error) {
                console.error('登录失败:', error);
                this.loginError = error.response?.data?.detail || '登录失败，请检查用户名和密码';
            } finally {
                this.isLoading = false;
            }
        },
        
        logout() {
            // 清除本地存储
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');
            
            // 重置状态
            this.isLoggedIn = false;
            this.token = null;
            this.currentUser = null;
            
            // 清除axios默认头部
            delete axios.defaults.headers.common['Authorization'];
        },
        
        // 数据加载方法
        async loadDashboardData() {
            this.isLoading = true;
            
            try {
                // 获取统计数据
                const response = await axios.get(`${API_BASE_URL}/stats`);
                this.stats = response.data;
            } catch (error) {
                console.error('加载仪表盘数据失败:', error);
                // 使用模拟数据
                this.stats = {
                    userCount: 0,
                    novelCount: 0,
                    activeAnnouncementCount: 0
                };
            } finally {
                this.isLoading = false;
            }
        },
        
        async loadAnnouncements() {
            this.isLoading = true;
            
            try {
                const response = await axios.get(`${API_BASE_URL}/announcements`);
                this.announcements = response.data;
                
                // 更新统计数据中的活跃公告数
                this.stats.activeAnnouncementCount = this.announcements.filter(a => a.is_active).length;
            } catch (error) {
                console.error('加载公告失败:', error);
            } finally {
                this.isLoading = false;
            }
        },
        
        async loadUsers() {
            this.isLoading = true;
            
            try {
                const response = await axios.get(`${API_BASE_URL}/users`);
                this.users = response.data;
                
                // 更新统计数据中的用户数
                this.stats.userCount = this.users.length;
            } catch (error) {
                console.error('加载用户失败:', error);
            } finally {
                this.isLoading = false;
            }
        },
        
        // 公告相关方法
        showAnnouncementModal(announcement = null) {
            if (announcement) {
                // 编辑现有公告
                this.editingAnnouncement = { ...announcement };
            } else {
                // 创建新公告
                this.editingAnnouncement = {
                    id: null,
                    title: '',
                    content: '',
                    is_important: false,
                    is_active: true
                };
            }
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('announcementModal'));
            modal.show();
        },
        
        async saveAnnouncement() {
            this.isLoading = true;
            
            try {
                let response;
                
                if (this.editingAnnouncement.id) {
                    // 更新现有公告
                    response = await axios.put(
                        `${API_BASE_URL}/announcements/${this.editingAnnouncement.id}`,
                        this.editingAnnouncement
                    );
                    
                    // 更新本地数据
                    const index = this.announcements.findIndex(a => a.id === this.editingAnnouncement.id);
                    if (index !== -1) {
                        this.announcements[index] = response.data;
                    }
                } else {
                    // 创建新公告
                    response = await axios.post(
                        `${API_BASE_URL}/announcements`,
                        this.editingAnnouncement
                    );
                    
                    // 添加到本地数据
                    this.announcements.unshift(response.data);
                }
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('announcementModal'));
                modal.hide();
                
                // 更新统计数据中的活跃公告数
                this.stats.activeAnnouncementCount = this.announcements.filter(a => a.is_active).length;
                
            } catch (error) {
                console.error('保存公告失败:', error);
                alert('保存公告失败: ' + (error.response?.data?.detail || '未知错误'));
            } finally {
                this.isLoading = false;
            }
        },
        
        async deleteAnnouncement(id) {
            if (!confirm('确定要删除这条公告吗？此操作不可撤销。')) {
                return;
            }
            
            this.isLoading = true;
            
            try {
                await axios.delete(`${API_BASE_URL}/announcements/${id}`);
                
                // 从本地数据中移除
                this.announcements = this.announcements.filter(a => a.id !== id);
                
                // 更新统计数据中的活跃公告数
                this.stats.activeAnnouncementCount = this.announcements.filter(a => a.is_active).length;
                
            } catch (error) {
                console.error('删除公告失败:', error);
                alert('删除公告失败: ' + (error.response?.data?.detail || '未知错误'));
            } finally {
                this.isLoading = false;
            }
        },
        
        // 用户相关方法
        showUserModal(user) {
            // 编辑用户
            this.editingUser = { 
                ...user,
                password: '' // 不显示密码
            };
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('userModal'));
            modal.show();
        },
        
        async saveUser() {
            this.isLoading = true;
            
            try {
                // 创建要发送的数据对象
                const userData = { ...this.editingUser };
                
                // 如果密码为空，则不发送密码字段
                if (!userData.password) {
                    delete userData.password;
                }
                
                // 更新用户
                const response = await axios.put(
                    `${API_BASE_URL}/users/${this.editingUser.id}`,
                    userData
                );
                
                // 更新本地数据
                const index = this.users.findIndex(u => u.id === this.editingUser.id);
                if (index !== -1) {
                    this.users[index] = response.data;
                }
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
                modal.hide();
                
            } catch (error) {
                console.error('保存用户失败:', error);
                alert('保存用户失败: ' + (error.response?.data?.detail || '未知错误'));
            } finally {
                this.isLoading = false;
            }
        },
        
        async toggleUserStatus(userId, isActive) {
            this.isLoading = true;
            
            try {
                // 更新用户状态
                const response = await axios.put(
                    `${API_BASE_URL}/users/${userId}`,
                    { is_active: isActive }
                );
                
                // 更新本地数据
                const index = this.users.findIndex(u => u.id === userId);
                if (index !== -1) {
                    this.users[index] = response.data;
                }
                
            } catch (error) {
                console.error('更新用户状态失败:', error);
                alert('更新用户状态失败: ' + (error.response?.data?.detail || '未知错误'));
            } finally {
                this.isLoading = false;
            }
        },
        
        // 设置相关方法
        async saveSettings() {
            this.isLoading = true;
            
            try {
                // 保存设置
                await axios.post(`${API_BASE_URL}/settings`, this.settings);
                alert('设置已保存');
                
            } catch (error) {
                console.error('保存设置失败:', error);
                alert('保存设置失败: ' + (error.response?.data?.detail || '未知错误'));
            } finally {
                this.isLoading = false;
            }
        },
        
        // 工具方法
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
    }
});

// 挂载Vue应用
app.mount('#app');

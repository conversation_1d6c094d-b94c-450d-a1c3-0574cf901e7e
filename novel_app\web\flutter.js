// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/**
 * This script loads the Flutter web app.
 */
"use strict";

/**
 * Creates a `<script>` tag for a Flutter app entrypoint.
 */
function _createFlutterBaseScript(baseUrl, entrypointUrl) {
  const script = document.createElement("script");
  script.type = "application/javascript";
  script.src = entrypointUrl;
  return script;
}

/**
 * Adds a callback to a DOM element.
 * @param {Element} element
 * @param {string} name
 * @param {function} callback
 */
function _addCallback(element, name, callback) {
  element.addEventListener(name, callback);
}

/**
 * The Flutter app entrypoint.
 */
const entrypointUrl = "main.dart.js";

/**
 * Handles the browser's `load` event.
 */
window.addEventListener("load", function (e) {
  const baseScript = _createFlutterBaseScript(
    "",
    entrypointUrl
  );
  document.body.append(baseScript);
});

/**
 * Flutter loader.
 */
window.flutter = window.flutter || {};
window.flutter.loader = {
  /**
   * Loads the Flutter app.
   * @param {Object} options
   * @return {Promise<void>}
   */
  load: function (options) {
    return new Promise(function (resolve, reject) {
      // Create a new script element for the Flutter app.
      const baseScript = _createFlutterBaseScript(
        "",
        entrypointUrl
      );

      // Add the script to the body.
      document.body.append(baseScript);

      // Create a new object that will be used to resolve the Promise.
      const appRunner = {
        // Runs the Flutter app.
        runApp: function () {
          // The Flutter app is already running, so just resolve the Promise.
          resolve();
        }
      };

      // When the script is loaded, resolve the Promise.
      baseScript.addEventListener("load", function () {
        resolve(appRunner);
      });

      // If the script fails to load, reject the Promise.
      baseScript.addEventListener("error", function (e) {
        reject(e);
      });
    });
  }
};

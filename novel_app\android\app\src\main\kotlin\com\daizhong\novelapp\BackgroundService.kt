package com.daizhong.novelapp

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat

/**
 * 后台服务，用于保持应用在后台运行时不被系统杀死
 */
class BackgroundService : Service() {
    companion object {
        private const val TAG = "BackgroundService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "novel_app_service"
        private const val CHANNEL_NAME = "岱宗文脉服务"

        // 唤醒锁标识
        private const val WAKE_LOCK_TAG = "NovelApp:BackgroundServiceWakeLock"

        // 服务是否正在运行
        private var isServiceRunning = false

        /**
         * 启动服务
         */
        fun startService(context: Context) {
            if (!isServiceRunning) {
                Log.d(TAG, "启动后台服务")
                val intent = Intent(context, BackgroundService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
                isServiceRunning = true
            } else {
                Log.d(TAG, "后台服务已在运行")
            }
        }

        /**
         * 停止服务
         */
        fun stopService(context: Context) {
            if (isServiceRunning) {
                Log.d(TAG, "停止后台服务")
                val intent = Intent(context, BackgroundService::class.java)
                context.stopService(intent)
                isServiceRunning = false
            } else {
                Log.d(TAG, "后台服务未在运行")
            }
        }

        /**
         * 获取服务是否正在运行
         */
        fun isRunning(): Boolean {
            return isServiceRunning
        }
    }

    // 唤醒锁
    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "后台服务创建")

        // 创建通知渠道
        createNotificationChannel()

        // 获取唤醒锁
        acquireWakeLock()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "后台服务启动")

        try {
            // 创建通知
            val notification = createNotification()

            // 启动前台服务
            startForeground(NOTIFICATION_ID, notification)
            Log.d(TAG, "前台服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动前台服务失败", e)
        }

        // 如果服务被系统杀死，重新启动
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        Log.d(TAG, "后台服务销毁")

        // 释放唤醒锁
        releaseWakeLock()

        // 重置服务状态
        isServiceRunning = false

        super.onDestroy()
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "保持应用在后台运行"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        try {
            // 创建点击通知时打开应用的Intent
            val pendingIntent = PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
                },
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )

            // 构建通知
            return NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("岱宗文脉正在后台运行")
                .setContentText("保持连接以确保小说生成不中断")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build()
        } catch (e: Exception) {
            Log.e(TAG, "创建通知失败", e)

            // 创建一个简单的通知作为备用
            return NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("岱宗文脉")
                .setContentText("应用正在后台运行")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build()
        }
    }

    /**
     * 获取唤醒锁
     */
    private fun acquireWakeLock() {
        if (wakeLock == null) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                WAKE_LOCK_TAG
            ).apply {
                setReferenceCounted(false)
            }
        }

        wakeLock?.let {
            if (!it.isHeld) {
                it.acquire(10*60*1000L) // 10分钟超时
                Log.d(TAG, "获取唤醒锁")
            }
        }
    }

    /**
     * 释放唤醒锁
     */
    private fun releaseWakeLock() {
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
                Log.d(TAG, "释放唤醒锁")
            }
        }
        wakeLock = null
    }
}

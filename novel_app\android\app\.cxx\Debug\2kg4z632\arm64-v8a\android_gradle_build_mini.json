{"buildFiles": ["D:\\element\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel_app002\\novel_app\\android\\app\\.cxx\\Debug\\2kg4z632\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel_app002\\novel_app\\android\\app\\.cxx\\Debug\\2kg4z632\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
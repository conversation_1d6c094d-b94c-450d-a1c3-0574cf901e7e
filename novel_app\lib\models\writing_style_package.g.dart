// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'writing_style_package.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WritingStylePackageAdapter extends TypeAdapter<WritingStylePackage> {
  @override
  final int typeId = 5;

  @override
  WritingStylePackage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WritingStylePackage(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      sampleTexts: (fields[3] as List).cast<String>(),
      author: fields[4] as String,
      createdAt: fields[5] as DateTime,
      updatedAt: fields[6] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, WritingStylePackage obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.sampleTexts)
      ..writeByte(4)
      ..write(obj.author)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WritingStylePackageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

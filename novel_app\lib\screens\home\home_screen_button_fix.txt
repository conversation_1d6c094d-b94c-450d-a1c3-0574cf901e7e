﻿                    } else if (controller.generationStage.value ==
                        GenerationStage.idle) {
                      return AnimatedButton(
                        onPressed: controller.generateOutlineWrapper,
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.auto_stories),
                            SizedBox(width: 8),
                            Text('鐢熸垚澶х翰'),
                          ],
                        ),
                      );
                    } else if (controller.generationStage.value ==
                        GenerationStage.generationComplete) {
                      // 鐢熸垚瀹屾垚鍚庢樉绀?閲嶆柊寮€濮?鎸夐挳
                      return AnimatedButton(
                        onPressed: () {
                          // 閲嶇疆鐢熸垚闃舵涓篿dle锛屼絾涓嶆竻闄ょ紦瀛?                          controller.generationStage.value = GenerationStage.idle;
                        },
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.refresh),
                            SizedBox(width: 8),
                            Text('閲嶆柊寮€濮?),
                          ],
                        ),
                      );
                    } else {
                      return Container();
                    }


server {
    listen 80;
    server_name www.dznovel.top dznovel.top;

    root /path/to/your/flutter/web/build;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/wasm;

    # 处理Flutter路由
    location / {
        try_files $uri $uri/ /index.html;

        # 添加CORS头
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

        # 添加内容安全策略头
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://*.dznovel.top https://*.openai.com https://*.aliyuncs.com https://*.googleapis.com https://*.baidubce.com https://*.volces.com https://*.siliconflow.cn https://*.deepseek.com https://www.gstatic.com *; worker-src 'self' blob:;";
    }

    # 确保flutter.js不被缓存
    location = /flutter.js {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, post-check=0, pre-check=0";
        add_header Pragma "no-cache";

        # 添加CORS头
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
    }

    # 特别处理CanvasKit文件
    location /canvaskit/ {
        expires max;
        add_header Cache-Control "public, max-age=31536000";

        # 添加CORS头
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

        # 确保正确的MIME类型
        types {
            application/javascript js;
            application/wasm wasm;
        }
    }

    # 确保其他静态资源可以被访问并设置长期缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|wasm)$ {
        expires max;
        access_log off;
        add_header Cache-Control "public, max-age=31536000";

        # 添加CORS头
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
    }
}

import 'package:get/get.dart';

/// 嵌入模型API格式枚举
enum EmbeddingApiFormat {
  openAiCompatible('OpenAI API兼容'),
  googleApi('Google API'),
  aliBailian('阿里百炼'),
  azureOpenAi('Azure OpenAI'),
  ollama('Ollama'),
  mlStudio('ML Studio'),
  gemini('Gemini'),
  siliconFlow('SiliconFlow');

  final String displayName;
  const EmbeddingApiFormat(this.displayName);

  static EmbeddingApiFormat fromString(String value) {
    return EmbeddingApiFormat.values.firstWhere(
      (format) => format.displayName == value,
      orElse: () => EmbeddingApiFormat.openAiCompatible,
    );
  }

  @override
  String toString() => displayName;
}

/// 嵌入模型配置类，用于存储嵌入模型的相关配置
class EmbeddingModelConfig {
  String name; // 嵌入模型名称
  String apiKey; // API密钥
  String baseUrl; // API基础URL
  String apiPath; // API路径
  String modelName; // 具体模型名称
  String apiFormat; // API格式（如OpenAI API兼容等）
  int topK; // 检索时返回的最相似文档数量
  bool enabled; // 是否启用嵌入模型
  bool useProxy; // 是否使用代理
  String proxyUrl; // 代理服务器URL
  int timeout; // 请求超时时间（秒）
  int maxChunkSize; // 文本分块最大长度
  double similarityThreshold; // 相似度阈值

  EmbeddingModelConfig({
    required this.name,
    required this.apiKey,
    required this.baseUrl,
    required this.apiPath,
    required this.modelName,
    required this.apiFormat,
    this.topK = 5,
    this.enabled = false,
    this.useProxy = false,
    this.proxyUrl = '',
    this.timeout = 30,
    this.maxChunkSize = 500,
    this.similarityThreshold = 0.7,
  });

  /// 获取API格式枚举
  EmbeddingApiFormat get apiFormatEnum =>
      EmbeddingApiFormat.fromString(apiFormat);

  Map<String, dynamic> toJson() => {
        'name': name,
        'apiKey': apiKey,
        'baseUrl': baseUrl,
        'apiPath': apiPath,
        'modelName': modelName,
        'apiFormat': apiFormat,
        'topK': topK,
        'enabled': enabled,
        'useProxy': useProxy,
        'proxyUrl': proxyUrl,
        'timeout': timeout,
        'maxChunkSize': maxChunkSize,
        'similarityThreshold': similarityThreshold,
      };

  factory EmbeddingModelConfig.fromJson(Map<String, dynamic> json) =>
      EmbeddingModelConfig(
        name: json['name'] as String? ?? '',
        apiKey: json['apiKey'] as String? ?? '',
        baseUrl: json['baseUrl'] as String? ?? '',
        apiPath: json['apiPath'] as String? ?? '',
        modelName: json['modelName'] as String? ?? '',
        apiFormat: json['apiFormat'] as String? ?? 'OpenAI API兼容',
        topK: (json['topK'] as num?)?.toInt() ?? 5,
        enabled: json['enabled'] as bool? ?? false,
        useProxy: json['useProxy'] as bool? ?? false,
        proxyUrl: json['proxyUrl'] as String? ?? '',
        timeout: (json['timeout'] as num?)?.toInt() ?? 30,
        maxChunkSize: (json['maxChunkSize'] as num?)?.toInt() ?? 500,
        similarityThreshold:
            (json['similarityThreshold'] as num?)?.toDouble() ?? 0.7,
      );

  EmbeddingModelConfig copyWith({
    String? name,
    String? apiKey,
    String? baseUrl,
    String? apiPath,
    String? modelName,
    String? apiFormat,
    int? topK,
    bool? enabled,
    bool? useProxy,
    String? proxyUrl,
    int? timeout,
    int? maxChunkSize,
    double? similarityThreshold,
  }) {
    return EmbeddingModelConfig(
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      baseUrl: baseUrl ?? this.baseUrl,
      apiPath: apiPath ?? this.apiPath,
      modelName: modelName ?? this.modelName,
      apiFormat: apiFormat ?? this.apiFormat,
      topK: topK ?? this.topK,
      enabled: enabled ?? this.enabled,
      useProxy: useProxy ?? this.useProxy,
      proxyUrl: proxyUrl ?? this.proxyUrl,
      timeout: timeout ?? this.timeout,
      maxChunkSize: maxChunkSize ?? this.maxChunkSize,
      similarityThreshold: similarityThreshold ?? this.similarityThreshold,
    );
  }

  /// 获取默认的嵌入模型配置
  static EmbeddingModelConfig getDefault() {
    return EmbeddingModelConfig(
      name: '岱宗官方嵌入模型（限时免费）',
      apiKey: 'daizong_official_embedding_key', // 使用特殊标记，表示使用岱宗官方API密钥
      baseUrl: 'https://dashscope.aliyuncs.com',
      apiPath: '/compatible-mode/v1/embeddings',
      modelName: 'text-embedding-v3',
      apiFormat: 'OpenAI API兼容',
      topK: 5,
      enabled: true, // 默认启用
      useProxy: false,
      proxyUrl: '',
      timeout: 60,
      maxChunkSize: 500,
      similarityThreshold: 0.7,
    );
  }

  /// 获取自定义嵌入模型配置
  static EmbeddingModelConfig getCustom() {
    return EmbeddingModelConfig(
      name: '自定义嵌入模型',
      apiKey: '',
      baseUrl: 'https://dashscope.aliyuncs.com',
      apiPath: '/compatible-mode/v1/embeddings',
      modelName: 'text-embedding-v3',
      apiFormat: 'OpenAI API兼容',
      topK: 5,
      enabled: false,
      useProxy: false,
      proxyUrl: '',
      timeout: 60,
      maxChunkSize: 500,
      similarityThreshold: 0.7,
    );
  }
}

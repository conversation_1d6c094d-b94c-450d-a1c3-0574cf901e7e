import 'dart:math' as math;

/// 文本分段工具类，用于将文本分割成适合向量化的片段
class TextSplitter {
  /// 将文本分割成段落
  ///
  /// [text] 要分割的文本
  /// [separator] 段落分隔符，默认为换行符
  static List<String> splitIntoParagraphs(String text,
      {String separator = '\n\n'}) {
    if (text.trim().isEmpty) {
      return [];
    }

    final paragraphs = text
        .split(separator)
        .map((p) => p.trim())
        .where((p) => p.isNotEmpty)
        .toList();

    return paragraphs;
  }

  /// 将文本分割成句子
  ///
  /// [text] 要分割的文本
  static List<String> splitIntoSentences(String text) {
    if (text.trim().isEmpty) {
      return [];
    }

    // 简单的句子分割规则
    final sentenceRegex = RegExp(r'(?<=[.!?。！？…])\s+');
    final sentences = text
        .split(sentenceRegex)
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    return sentences;
  }

  /// 将文本分割成适合向量化的片段
  ///
  /// [text] 要分割的文本
  /// [maxLength] 每个片段的最大长度
  /// [overlap] 片段之间的重叠长度
  /// [similarityThreshold] 相似度阈值，用于合并相似的片段
  static List<String> splitForVectorization(
    String text, {
    int maxLength = 500,
    int overlap = 100,
    double similarityThreshold = 0.7,
  }) {
    if (text.trim().isEmpty) {
      return [];
    }

    // 如果文本长度小于最大长度，直接返回
    if (text.length <= maxLength) {
      return [text];
    }

    // 先按段落分割
    final paragraphs = splitIntoParagraphs(text);

    // 如果段落太长，再按句子分割
    final List<String> segments = [];
    for (final paragraph in paragraphs) {
      if (paragraph.length <= maxLength) {
        segments.add(paragraph);
      } else {
        // 按句子分割
        final sentences = splitIntoSentences(paragraph);

        // 合并句子，直到达到最大长度
        String currentSegment = '';
        for (final sentence in sentences) {
          if (currentSegment.isEmpty) {
            currentSegment = sentence;
          } else if (currentSegment.length + sentence.length + 1 <= maxLength) {
            currentSegment += ' ' + sentence;
          } else {
            segments.add(currentSegment);

            // 如果设置了重叠，从当前段落的末尾取一部分作为新段落的开始
            if (overlap > 0 && currentSegment.length > overlap) {
              // 尝试找到一个句子边界作为重叠点
              final lastSentences = splitIntoSentences(currentSegment
                  .substring(math.max(0, currentSegment.length - overlap * 2)));

              if (lastSentences.isNotEmpty) {
                // 使用最后几个句子作为重叠
                final overlapText = lastSentences.length > 1
                    ? lastSentences.sublist(lastSentences.length - 2).join(' ')
                    : lastSentences.last;
                currentSegment = overlapText;
              } else {
                // 如果找不到句子边界，直接使用最后的字符
                currentSegment = currentSegment
                    .substring(math.max(0, currentSegment.length - overlap));
              }
            } else {
              currentSegment = '';
            }

            // 添加当前句子
            if (currentSegment.isEmpty) {
              currentSegment = sentence;
            } else {
              currentSegment += ' ' + sentence;
            }
          }
        }

        // 添加最后一个片段
        if (currentSegment.isNotEmpty) {
          segments.add(currentSegment);
        }
      }
    }

    // 处理大型文档：如果段落数量很多，可能需要进一步合并
    if (segments.length > 100) {
      // 分批处理，每批50个段落
      final List<String> batchedSegments = [];
      for (int i = 0; i < segments.length; i += 50) {
        final endIndex = math.min(i + 50, segments.length);
        final batch = segments.sublist(i, endIndex);

        // 合并批次内的段落
        String batchText = batch.join('\n\n');
        if (batchText.length <= maxLength * 2) {
          batchedSegments.add(batchText);
        } else {
          // 如果合并后太长，再次分割
          batchedSegments.addAll(_resplitLongText(
            batchText,
            maxLength: maxLength,
            overlap: overlap,
          ));
        }
      }

      // 更新段落列表
      segments.clear();
      segments.addAll(batchedSegments);
    }

    // 如果启用了相似度合并，尝试合并相似的片段
    if (similarityThreshold > 0 && similarityThreshold < 1) {
      return _mergeSimilarSegments(segments, similarityThreshold, maxLength);
    }

    return segments;
  }

  /// 重新分割长文本
  static List<String> _resplitLongText(
    String text, {
    required int maxLength,
    required int overlap,
  }) {
    final List<String> chunks = [];

    // 如果文本长度小于最大长度，直接返回
    if (text.length <= maxLength) {
      return [text];
    }

    // 按句子分割
    final sentences = splitIntoSentences(text);

    String currentChunk = '';
    for (final sentence in sentences) {
      // 如果当前块加上新句子超过最大长度，保存当前块并开始新块
      if (currentChunk.isNotEmpty &&
          currentChunk.length + sentence.length + 1 > maxLength) {
        chunks.add(currentChunk);

        // 如果设置了重叠，从当前块的末尾取一部分作为新块的开始
        if (overlap > 0 && currentChunk.length > overlap) {
          currentChunk = currentChunk
              .substring(math.max(0, currentChunk.length - overlap));
        } else {
          currentChunk = '';
        }
      }

      // 添加句子到当前块
      if (currentChunk.isEmpty) {
        currentChunk = sentence;
      } else {
        currentChunk += ' ' + sentence;
      }

      // 如果单个句子超过最大长度，需要按字符分割
      if (currentChunk.length > maxLength) {
        int startIndex = 0;
        while (startIndex < currentChunk.length) {
          final endIndex =
              math.min(startIndex + maxLength, currentChunk.length);
          chunks.add(currentChunk.substring(startIndex, endIndex));

          // 如果设置了重叠，从当前块的末尾取一部分作为新块的开始
          if (overlap > 0 && endIndex < currentChunk.length) {
            startIndex = endIndex - overlap;
          } else {
            startIndex = endIndex;
          }
        }

        currentChunk = '';
      }
    }

    // 添加最后一个块
    if (currentChunk.isNotEmpty) {
      chunks.add(currentChunk);
    }

    return chunks;
  }

  /// 合并相似的文本片段
  ///
  /// [segments] 要合并的片段列表
  /// [similarityThreshold] 相似度阈值
  /// [maxLength] 合并后的最大长度
  static List<String> _mergeSimilarSegments(
    List<String> segments,
    double similarityThreshold,
    int maxLength,
  ) {
    if (segments.length <= 1) {
      return segments;
    }

    final List<String> result = [];
    String currentSegment = segments[0];

    for (int i = 1; i < segments.length; i++) {
      final nextSegment = segments[i];

      // 计算简单的相似度（基于共同词汇）
      final similarity =
          _calculateSimpleSimilarity(currentSegment, nextSegment);

      // 如果相似度高于阈值且合并后不超过最大长度，则合并
      if (similarity >= similarityThreshold &&
          currentSegment.length + nextSegment.length <= maxLength) {
        currentSegment += '\n' + nextSegment;
      } else {
        // 否则添加当前片段并开始新片段
        result.add(currentSegment);
        currentSegment = nextSegment;
      }
    }

    // 添加最后一个片段
    if (currentSegment.isNotEmpty) {
      result.add(currentSegment);
    }

    return result;
  }

  /// 计算两个文本的简单相似度（基于共同词汇）
  ///
  /// [text1] 第一个文本
  /// [text2] 第二个文本
  static double _calculateSimpleSimilarity(String text1, String text2) {
    // 将文本转换为词集合
    final words1 = text1
        .toLowerCase()
        .split(RegExp(r'\s+'))
        .where((w) => w.isNotEmpty)
        .toSet();
    final words2 = text2
        .toLowerCase()
        .split(RegExp(r'\s+'))
        .where((w) => w.isNotEmpty)
        .toSet();

    // 计算交集和并集
    final intersection = words1.intersection(words2);
    final union = words1.union(words2);

    // 计算Jaccard相似度
    if (union.isEmpty) {
      return 0.0;
    }

    return intersection.length / union.length;
  }

  /// 计算两个向量的余弦相似度
  ///
  /// [vec1] 第一个向量
  /// [vec2] 第二个向量
  static double calculateCosineSimilarity(
      List<double> vec1, List<double> vec2) {
    if (vec1.length != vec2.length) {
      throw Exception('向量长度不一致: ${vec1.length} vs ${vec2.length}');
    }

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }

    // 避免除以零
    if (norm1 == 0 || norm2 == 0) {
      return 0.0;
    }

    return dotProduct / (math.sqrt(norm1) * math.sqrt(norm2));
  }
}

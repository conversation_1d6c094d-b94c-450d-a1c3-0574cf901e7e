﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{940812A4-58E2-3F92-AC73-41EFA4C67CE2}"
	ProjectSection(ProjectDependencies) = postProject
		{9D247693-8023-3B8E-9A5F-7D31506338F8} = {9D247693-8023-3B8E-9A5F-7D31506338F8}
		{CB572C94-B0B7-3C53-AD97-FDE417310752} = {CB572C94-B0B7-3C53-AD97-FDE417310752}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{4726F64A-DAF3-3D9B-8583-EB1EBD409A03}"
	ProjectSection(ProjectDependencies) = postProject
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2} = {940812A4-58E2-3F92-AC73-41EFA4C67CE2}
		{9D247693-8023-3B8E-9A5F-7D31506338F8} = {9D247693-8023-3B8E-9A5F-7D31506338F8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{9D247693-8023-3B8E-9A5F-7D31506338F8}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}"
	ProjectSection(ProjectDependencies) = postProject
		{9D247693-8023-3B8E-9A5F-7D31506338F8} = {9D247693-8023-3B8E-9A5F-7D31506338F8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{2770794D-D640-3BD8-AEC2-22F6BD8189A9}"
	ProjectSection(ProjectDependencies) = postProject
		{9D247693-8023-3B8E-9A5F-7D31506338F8} = {9D247693-8023-3B8E-9A5F-7D31506338F8}
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398} = {BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "just_audio_windows_plugin", "just_audio_windows_plugin.vcxproj", "{CB572C94-B0B7-3C53-AD97-FDE417310752}"
	ProjectSection(ProjectDependencies) = postProject
		{9D247693-8023-3B8E-9A5F-7D31506338F8} = {9D247693-8023-3B8E-9A5F-7D31506338F8}
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398} = {BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9} = {2770794D-D640-3BD8-AEC2-22F6BD8189A9}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Debug|x64.ActiveCfg = Debug|x64
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Debug|x64.Build.0 = Debug|x64
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Profile|x64.ActiveCfg = Profile|x64
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Profile|x64.Build.0 = Profile|x64
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Release|x64.ActiveCfg = Release|x64
		{940812A4-58E2-3F92-AC73-41EFA4C67CE2}.Release|x64.Build.0 = Release|x64
		{4726F64A-DAF3-3D9B-8583-EB1EBD409A03}.Debug|x64.ActiveCfg = Debug|x64
		{4726F64A-DAF3-3D9B-8583-EB1EBD409A03}.Profile|x64.ActiveCfg = Profile|x64
		{4726F64A-DAF3-3D9B-8583-EB1EBD409A03}.Release|x64.ActiveCfg = Release|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Debug|x64.ActiveCfg = Debug|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Debug|x64.Build.0 = Debug|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Profile|x64.ActiveCfg = Profile|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Profile|x64.Build.0 = Profile|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Release|x64.ActiveCfg = Release|x64
		{9D247693-8023-3B8E-9A5F-7D31506338F8}.Release|x64.Build.0 = Release|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Debug|x64.ActiveCfg = Debug|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Debug|x64.Build.0 = Debug|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Profile|x64.ActiveCfg = Profile|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Profile|x64.Build.0 = Profile|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Release|x64.ActiveCfg = Release|x64
		{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}.Release|x64.Build.0 = Release|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Debug|x64.ActiveCfg = Debug|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Debug|x64.Build.0 = Debug|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Profile|x64.ActiveCfg = Profile|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Profile|x64.Build.0 = Profile|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Release|x64.ActiveCfg = Release|x64
		{2770794D-D640-3BD8-AEC2-22F6BD8189A9}.Release|x64.Build.0 = Release|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Debug|x64.ActiveCfg = Debug|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Debug|x64.Build.0 = Debug|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Profile|x64.ActiveCfg = Profile|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Profile|x64.Build.0 = Profile|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Release|x64.ActiveCfg = Release|x64
		{CB572C94-B0B7-3C53-AD97-FDE417310752}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8943609E-6D27-3142-BA99-3BE8D46197B8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/launch_background"
    tools:context=".SplashActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/logoImageView"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="40dp"
            android:src="@drawable/launch_logo" />

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="岱宗文脉"
            android:textColor="@android:color/white"
            android:textSize="28sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/sloganTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="汲取泰山灵气，承载文脉传承"
            android:textColor="@android:color/white"
            android:textSize="16sp" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="60dp" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="240dp"
            android:layout_height="wrap_content"
            android:progressBackgroundTint="@color/secondary"
            android:progressTint="@android:color/white" />

        <TextView
            android:id="@+id/statusTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="初始化中..."
            android:textColor="@android:color/white"
            android:textSize="14sp" />
    </LinearLayout>

</RelativeLayout>

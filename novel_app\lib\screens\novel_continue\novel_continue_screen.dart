import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/novel.dart';

/// 小说续写界面
class NovelContinueScreen extends StatefulWidget {
  final Novel novel;
  final int chapterIndex;

  const NovelContinueScreen({
    super.key,
    required this.novel,
    required this.chapterIndex,
  });

  // 从参数Map创建
  factory NovelContinueScreen.fromArguments(Map<String, dynamic> args) {
    return NovelContinueScreen(
      novel: args['novel'] as Novel,
      chapterIndex: args['chapterIndex'] as int,
    );
  }

  @override
  State<NovelContinueScreen> createState() => _NovelContinueScreenState();
}

class _NovelContinueScreenState extends State<NovelContinueScreen> {
  final NovelController _novelController = Get.find<NovelController>();
  final KnowledgeBaseController _knowledgeBaseController =
      Get.find<KnowledgeBaseController>();

  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _wordCountController =
      TextEditingController(text: '1000');

  bool _isGenerating = false;
  String _generatedContent = '';
  bool _showStreamOutput = false;

  @override
  void initState() {
    super.initState();
    // 加载章节内容
    _loadChapterContent();
  }

  @override
  void dispose() {
    _contentController.dispose();
    _wordCountController.dispose();
    super.dispose();
  }

  /// 加载章节内容
  Future<void> _loadChapterContent() async {
    final chapter = widget.novel.chapters[widget.chapterIndex];
    if (chapter.content.isNotEmpty) {
      _contentController.text = chapter.content;
    }
  }

  /// 续写内容
  Future<void> _continueContent() async {
    try {
      setState(() {
        _isGenerating = true;
        _showStreamOutput = true;
        _generatedContent = '';
      });

      // 检查是否有未生成的章节
      final result =
          await _novelController.checkAndContinueNovelGeneration(widget.novel);

      if (result == 'completed') {
        // 如果所有章节都已生成，则进行当前章节的续写
        // 暂时显示提示信息
        Get.snackbar('提示', '所有章节已生成完毕，当前章节续写功能将在后续版本中实现');
      }
    } catch (e) {
      Get.snackbar('错误', '续写失败: $e');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  /// 应用生成的内容
  void _applyGeneratedContent() {
    if (_generatedContent.isEmpty) return;

    setState(() {
      _contentController.text += '\n\n$_generatedContent';
      _generatedContent = '';
      _showStreamOutput = false;
    });
  }

  /// 保存章节内容
  Future<void> _saveChapterContent() async {
    final chapter = widget.novel.chapters[widget.chapterIndex];
    chapter.content = _contentController.text;

    await _novelController.updateNovel(widget.novel);
    Get.snackbar('保存成功', '章节内容已保存');
  }

  @override
  Widget build(BuildContext context) {
    final chapter = widget.novel.chapters[widget.chapterIndex];

    return Scaffold(
      appBar: AppBar(
        title: Text('续写: ${chapter.title}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveChapterContent,
            tooltip: '保存章节内容',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _contentController,
                maxLines: null,
                decoration: const InputDecoration(
                  hintText: '输入要续写的内容...',
                  border: OutlineInputBorder(),
                ),
                readOnly: _isGenerating,
                expands: true,
              ),
            ),
          ),
          if (_showStreamOutput)
            Expanded(
              child: Card(
                margin: const EdgeInsets.all(16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('生成内容预览',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          IconButton(
                            icon: const Icon(Icons.check),
                            onPressed:
                                _isGenerating ? null : _applyGeneratedContent,
                            tooltip: '应用生成内容',
                          ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(_generatedContent),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                Obx(() => CheckboxListTile(
                      title: const Text('使用知识库'),
                      value: _knowledgeBaseController.useKnowledgeBase.value,
                      onChanged: (value) {
                        if (value != null) {
                          _knowledgeBaseController.useKnowledgeBase.value =
                              value;
                          _knowledgeBaseController.saveSettings();
                        }
                      },
                    )),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _wordCountController,
                        decoration: const InputDecoration(
                          labelText: '续写字数',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: _isGenerating ? null : _continueContent,
                      icon: _isGenerating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.play_arrow),
                      label: Text(_isGenerating ? '生成中...' : '开始续写'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

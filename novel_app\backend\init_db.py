import os
import uuid
from dotenv import load_dotenv
from app.database import SessionLocal, engine
from app.models import Base, User, Announcement
from app.auth import get_password_hash

# 加载环境变量
load_dotenv()

# 确保alembic目录存在
os.makedirs('alembic/versions', exist_ok=True)

def init_db():
    # 创建数据库表
    Base.metadata.create_all(bind=engine)

    # 获取数据库会话
    db = SessionLocal()

    try:
        # 检查是否已存在管理员用户
        admin_username = os.getenv("ADMIN_USERNAME", "admin")
        admin_exists = db.query(User).filter(User.username == admin_username).first()

        if not admin_exists:
            # 创建管理员用户
            admin_email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
            admin_password = os.getenv("ADMIN_PASSWORD", "admin")

            admin_user = User(
                username=admin_username,
                email=admin_email,
                hashed_password=get_password_hash(admin_password),
                is_active=True,
                is_admin=True
            )

            db.add(admin_user)
            db.commit()
            print(f"管理员用户 '{admin_username}' 已创建")
        else:
            print(f"管理员用户 '{admin_username}' 已存在")

        # 检查是否已存在欢迎公告
        welcome_announcement = db.query(Announcement).filter(Announcement.title == "欢迎使用岱宗文脉").first()

        if not welcome_announcement:
            # 创建欢迎公告
            welcome = Announcement(
                id=str(uuid.uuid4()),
                title="欢迎使用岱宗文脉",
                content="感谢您使用岱宗文脉小说生成器！\n\n本应用使用先进的AI技术，帮助您创作精彩的小说。\n\n如有任何问题或建议，请联系我们的客服。",
                is_important=True,
                is_active=True
            )

            db.add(welcome)
            db.commit()
            print("欢迎公告已创建")
        else:
            print("欢迎公告已存在")

    except Exception as e:
        print(f"初始化数据库时出错: {e}")
        db.rollback()

    finally:
        db.close()

if __name__ == "__main__":
    print("初始化数据库...")
    init_db()
    print("数据库初始化完成")

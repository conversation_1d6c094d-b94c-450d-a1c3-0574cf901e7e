^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\FILE_SELECTOR_WINDOWS\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/plugins/file_selector_windows/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

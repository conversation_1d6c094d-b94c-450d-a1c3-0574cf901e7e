from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
from dotenv import load_dotenv

from .routers import auth, novels, announcements, users, stats, settings, version
from .database import engine
from .models import Base

# 加载环境变量
load_dotenv()

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 创建应用
app = FastAPI(
    title="岱宗文脉API",
    description="岱宗文脉小说生成器后端API",
    version="1.0.0"
)

# 配置CORS - 允许所有来源访问API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=False,  # 当allow_origins=["*"]时，必须设为False
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
)

# 包含路由
app.include_router(auth.router)
app.include_router(novels.router)
app.include_router(announcements.router)
app.include_router(users.router)
app.include_router(stats.router)
app.include_router(settings.router)
app.include_router(version.router)

# 挂载管理后台静态文件
admin_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "admin")
if os.path.exists(admin_dir):
    app.mount("/admin", StaticFiles(directory=admin_dir, html=True), name="admin")

@app.get("/")
async def root():
    return {"message": "欢迎使用岱宗文脉API"}

@app.get("/api/health")
async def health_check():
    return {"status": "healthy"}

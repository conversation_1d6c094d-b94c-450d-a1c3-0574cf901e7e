﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{75465B57-D204-33EA-AE6F-18710146BD8B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{B9468CAC-3C80-35ED-BA5B-E07B8A34647F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

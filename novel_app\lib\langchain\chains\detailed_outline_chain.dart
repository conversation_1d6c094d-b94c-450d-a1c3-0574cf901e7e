import 'package:langchain/langchain.dart';
import '../prompts/novel_prompt_templates_enhanced.dart';

/// A chain specifically designed for generating a detailed plot outline for a single chapter.
class DetailedOutlineChain extends LLMChain {
  DetailedOutlineChain({required super.llm})
      : super(prompt: NovelPromptTemplates.detailedOutlinePrompt);

  /// Runs the chain for a single chapter's detailed outline generation.
  Future<String> generateDetailedOutlineForChapter(
      Map<String, dynamic> inputs) async {
    try {
      // Ensure all required inputs for the prompt are present
      final requiredKeys =
          NovelPromptTemplates.detailedOutlinePrompt.inputVariables;
      for (final key in requiredKeys) {
        if (!inputs.containsKey(key)) {
          // Provide default empty string if a non-critical key is missing, or throw error
          if (key == 'history') {
            // Example: History might not be needed
            inputs[key] = '';
          } else {
            print(
                "Warning: Missing required input key '$key' for DetailedOutlineChain. Providing empty string.");
            inputs[key] =
                ''; // Or throw an error: throw ArgumentError('Missing required input key: $key');
          }
        }
      }

      // 记录LLM类型
      if (llm.runtimeType.toString().contains('AliyunQwenAdapter')) {
        print("[DetailedOutlineChain] 检测到阿里云通义千问适配器");
      } else if (llm.toString().contains('dashscope.aliyuncs.com')) {
        print("[DetailedOutlineChain] 检测到阿里云通义千问模型");
      }

      String result;
      // 无论是什么模型，都使用流式模式，这样可以确保阿里云通义千问模型正常工作
      print("[DetailedOutlineChain] 使用流式模式生成细纲...");
      final buffer = StringBuffer();
      final resultStream = streamText(inputs);

      await for (final chunk in resultStream) {
        buffer.write(chunk);
      }

      result = buffer.toString();
      print("[DetailedOutlineChain] 流式生成细纲完成，内容长度: ${result.length}");

      return result;
    } catch (e) {
      print(
          'Error in DetailedOutlineChain for chapter ${inputs['chapterNumber']}: $e');
      rethrow; // Re-throw the error to be handled by the service layer
    }
  }

  /// 流式执行链并返回结果流
  Stream<String> streamText(Map<String, dynamic> inputs) async* {
    try {
      // 格式化提示词
      final formattedPrompt = prompt.format(inputs);
      final messages = [ChatMessage.humanText(formattedPrompt)];
      final promptValue = ChatPromptValue(messages);

      // 获取LLM的流式响应
      final streamingResponse = llm.stream(promptValue);

      // 处理流式响应
      await for (final chunk in streamingResponse) {
        if (chunk.generations.isNotEmpty) {
          final output = chunk.generations.first.output;
          // 根据输出类型获取内容
          String contentChunk = "";
          if (output is AIChatMessage) {
            contentChunk = output.contentAsString;
          } else if (output is Map) {
            contentChunk = output['content'] ?? output.toString();
          } else {
            contentChunk = output.toString();
          }
          yield contentChunk;
        }
      }
    } catch (e) {
      print('Error in DetailedOutlineChain.streamText: $e');
      rethrow;
    }
  }
}

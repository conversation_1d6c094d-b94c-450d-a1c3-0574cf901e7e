import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/services/embedding_service.dart';
import 'package:novel_app/models/chat_message.dart';
import 'package:novel_app/langchain/services/novel_generation_service.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/langchain/chains/novel_generation_chain.dart';
import 'package:novel_app/utils/text_splitter.dart';

/// 小说聊天服务
/// 使用嵌入模型优化的小说聊天功能
class NovelChatService extends GetxService {
  final ApiConfigController _apiConfigController;
  final NovelVectorizationService _vectorizationService;
  final ChatHistoryService _chatHistoryService;
  final NovelGenerationService _novelGenerationService;

  // 聊天状态
  final RxBool isGenerating = false.obs;

  NovelChatService({
    required ApiConfigController apiConfigController,
    required NovelVectorizationService vectorizationService,
    required ChatHistoryService chatHistoryService,
    required NovelGenerationService novelGenerationService,
  })  : _apiConfigController = apiConfigController,
        _vectorizationService = vectorizationService,
        _chatHistoryService = chatHistoryService,
        _novelGenerationService = novelGenerationService;

  /// 初始化服务
  Future<NovelChatService> init() async {
    print('[NovelChatService] 初始化成功');
    return this;
  }

  /// 生成聊天回复
  Future<String> generateChatResponse(String message, String novelTitle) async {
    if (isGenerating.value) {
      return "正在生成回复，请稍候...";
    }

    try {
      isGenerating.value = true;

      // 添加用户消息到历史记录
      final userMessage = ChatMessage.user(
        content: message,
        novelTitle: novelTitle,
      );
      await _chatHistoryService.addMessage(userMessage);

      // 检查小说是否已向量化
      final isVectorized = _vectorizationService.isNovelVectorized(novelTitle);

      // 如果启用了嵌入模型但小说未向量化，则先向量化
      if (_apiConfigController.embeddingModel.value.enabled && !isVectorized) {
        print('[NovelChatService] 小说未向量化，开始向量化...');
        await _vectorizationService.vectorizeNovel(novelTitle);
      }

      // 获取聊天上下文
      final context = await _buildChatContext(message, novelTitle);

      // 获取小说生成链
      final chain = await _novelGenerationService.getChatChain(novelTitle);

      // 生成回复
      final response = await chain.run({
        'task': 'chat',
        'novelTitle': novelTitle,
        'userMessage': message,
        'novelContent': context,
      });

      // 添加AI回复到历史记录
      final aiMessage = ChatMessage.ai(
        content: response,
        novelTitle: novelTitle,
      );
      await _chatHistoryService.addMessage(aiMessage);

      print('[NovelChatService] 生成回复成功，长度: ${response.length}');
      return response;
    } catch (e) {
      print('[NovelChatService] 生成回复失败: $e');

      // 添加错误消息到历史记录
      final errorMessage = ChatMessage.ai(
        content: "抱歉，生成回复时出现错误: $e",
        novelTitle: novelTitle,
      );
      await _chatHistoryService.addMessage(errorMessage);

      return "抱歉，生成回复时出现错误: $e";
    } finally {
      isGenerating.value = false;
    }
  }

  /// 构建聊天上下文
  Future<String> _buildChatContext(String message, String novelTitle) async {
    final buffer = StringBuffer();

    // 添加小说基本信息
    final novelMemory = NovelMemory(novelTitle: novelTitle);
    final outline = await novelMemory.getOutline();

    buffer.writeln('# 小说信息');
    buffer.writeln('标题: $novelTitle');

    if (outline != null && outline.isNotEmpty) {
      buffer.writeln('\n## 小说大纲');
      buffer.writeln(outline);
    }

    // 获取知识库内容
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();
    String knowledgeBaseString = '';
    if (knowledgeBaseController.useKnowledgeBase.value &&
        knowledgeBaseController.selectedDocIds.isNotEmpty) {
      knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();

      if (knowledgeBaseString.isNotEmpty) {
        buffer.writeln('\n# 知识库信息');
        buffer.writeln(knowledgeBaseString);
      }
    }

    // 如果启用了嵌入模型，使用语义检索获取相关内容
    if (_apiConfigController.embeddingModel.value.enabled &&
        _vectorizationService.isNovelVectorized(novelTitle)) {
      try {
        // 获取与用户问题最相关的小说内容
        final searchResults = await _vectorizationService.searchNovelContent(
          novelTitle,
          message,
          maxResults: _apiConfigController.embeddingModel.value.topK,
        );

        if (searchResults.isNotEmpty) {
          buffer.writeln('\n# 相关小说内容');

          // 按相似度排序
          searchResults.sort((a, b) =>
              (b['similarity'] as double).compareTo(a['similarity'] as double));

          // 只保留相似度高于阈值的结果
          final filteredResults = searchResults
              .where((result) =>
                  (result['similarity'] as double) >=
                  _apiConfigController.embeddingModel.value.similarityThreshold)
              .toList();

          for (final result in filteredResults) {
            final type = result['type'] as String;
            final title = result['title'] as String;
            final chapter = result['chapter'] as int;
            final content = result['content'] as String;
            final similarity = result['similarity'] as double;

            if (type == 'outline') {
              buffer.writeln(
                  '\n## 大纲 (相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
            } else {
              buffer.writeln(
                  '\n## 第$chapter章: $title (相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
            }

            buffer.writeln(content);
          }
        }

        // 如果没有找到相关内容或相关内容太少，添加一些基本内容
        if (searchResults.isEmpty || searchResults.length < 2) {
          // 获取前2章作为基本上下文
          for (int i = 1; i <= 2; i++) {
            final chapterTitle = await novelMemory.getChapterTitle(i);
            final chapterContent = await novelMemory.getChapter(i);

            if (chapterTitle != null && chapterContent != null) {
              buffer.writeln('\n## 第$i章: $chapterTitle (基础内容)');

              // 限制每章内容长度，避免上下文过长
              if (chapterContent.length > 1000) {
                buffer.writeln('${chapterContent.substring(0, 1000)}...');
              } else {
                buffer.writeln(chapterContent);
              }
            }
          }
        }
      } catch (e) {
        print('[NovelChatService] 获取相关内容失败: $e');

        // 如果检索失败，使用传统方式获取内容
        await _fallbackContextBuilding(buffer, novelMemory);
      }
    } else {
      // 如果未启用嵌入模型或小说未向量化，使用传统方式获取内容
      await _fallbackContextBuilding(buffer, novelMemory);
    }

    // 添加最近的聊天历史
    final chatHistory = _chatHistoryService.messages
        .where((msg) => msg.novelTitle == novelTitle)
        .toList();

    if (chatHistory.isNotEmpty) {
      buffer.writeln('\n# 最近对话历史');

      // 只取最近的10条消息
      final recentMessages = chatHistory.length > 10
          ? chatHistory.sublist(chatHistory.length - 10)
          : chatHistory;

      for (final msg in recentMessages) {
        final role = msg.type == ChatMessageType.user ? '用户' : 'AI';
        buffer.writeln('\n$role: ${msg.content}');
      }
    }

    return buffer.toString();
  }

  /// 传统方式构建上下文（当嵌入模型不可用时的备选方案）
  Future<void> _fallbackContextBuilding(
      StringBuffer buffer, NovelMemory novelMemory) async {
    // 获取前3章作为上下文
    for (int i = 1; i <= 3; i++) {
      final chapterTitle = await novelMemory.getChapterTitle(i);
      final chapterContent = await novelMemory.getChapter(i);

      if (chapterTitle != null && chapterContent != null) {
        buffer.writeln('\n## 第$i章: $chapterTitle');

        // 限制每章内容长度，避免上下文过长
        if (chapterContent.length > 2000) {
          buffer.writeln('${chapterContent.substring(0, 2000)}...');
        } else {
          buffer.writeln(chapterContent);
        }
      }
    }
  }
}

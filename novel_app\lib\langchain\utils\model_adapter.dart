import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/adapters/aliyun_qwen_adapter.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/ai_service.dart';

/// 模型适配器工具，将应用API配置转换为LangChain LLM模型
class ModelAdapter {
  /// 创建使用代理的HTTP客户端
  static http.Client _createProxyHttpClient(String proxyUrl, Duration timeout) {
    print('创建使用代理的HTTP客户端: $proxyUrl');

    // 解析代理地址和端口
    final parts = proxyUrl.split(':');
    if (parts.length != 2) {
      throw Exception('代理地址格式错误，应为 host:port，如 127.0.0.1:7890');
    }

    final proxyHost = parts[0];
    final proxyPort = int.tryParse(parts[1]);
    if (proxyPort == null) {
      throw Exception('代理端口格式错误: ${parts[1]}');
    }

    // 创建一个新的HttpClient并设置代理
    final httpClient = HttpClient();
    httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';
    httpClient.connectionTimeout = timeout;

    // 将HttpClient包装为http.Client
    return IOClient(httpClient);
  }

  /// 根据API配置创建合适的LLM实例
  static BaseChatModel createLLMFromConfig(ModelConfig config) {
    // Add logging here
    print('--- ModelAdapter createLLMFromConfig ---');
    print('Received config name: ${config.name}');
    print('Received config apiUrl: ${config.apiUrl}');
    print('Received config apiPath: ${config.apiPath}');
    print('Received config apiFormat: ${config.apiFormat}');
    print('Received config model: ${config.model}');
    print('--------------------------------------');

    // Default options from config
    final defaultOptions = ChatOpenAIOptions(
      model: config.model,
      temperature: config.temperature,
      maxTokens: config.maxTokens > 0
          ? config.maxTokens
          : null, // Pass null if maxTokens is not set or invalid
      topP: config.topP,
      // Note: repetitionPenalty in ModelConfig might map to frequencyPenalty or presencePenalty.
      // Check LangChain documentation for the best mapping. Using presencePenalty as an example:
      // presencePenalty: config.repetitionPenalty,
    );

    // 检查API格式
    if (config.apiFormat == 'Google API') {
      // Google API（Gemini）
      print('Google API detected, 使用自定义Google API适配器');

      // 使用自定义的Google API适配器
      return GoogleAPIAdapter(
        config: config,
        defaultOptions: defaultOptions,
      );
    } else if (config.appId.isNotEmpty) {
      // 百度千帆API
      // Assuming Qianfan also uses OpenAI compatible structure but needs specific headers
      // Check if LangChain has a specific Baidu Qianfan integration
      // Also ensure the baseUrl is correctly set for Qianfan's compatible endpoint
      // The default Qianfan config might need adjustment in ApiConfigController
      return ChatOpenAI(
        apiKey: config
            .apiKey, // Qianfan often uses Secret Key or other auth methods
        baseUrl: config
            .apiUrl, // Ensure this points to the correct base for chat completions endpoint
        defaultOptions: defaultOptions,
        headers: {
          // Adjust headers based on Qianfan documentation if needed
          // e.g., 'Authorization': 'Bearer ${config.apiKey}', // Example, verify correct auth method
        },
        // Qianfan might require query parameters, potentially involving access tokens
        // queryParams: { ... }
      );
    } else {
      // 通用OpenAI兼容API（包括通义千问、Deepseek等 using compatible mode）
      String effectiveBaseUrl = config.apiUrl;

      // Handle specific API path logic if apiPath is provided for compatible modes
      // This logic assumes ChatOpenAI will append '/chat/completions' or similar
      if (config.apiPath.isNotEmpty) {
        const String chatCompletionsSuffix =
            '/chat/completions'; // Common suffix
        if (config.apiPath.endsWith(chatCompletionsSuffix)) {
          // Construct the base URL by combining apiUrl and the path prefix before '/chat/completions'
          String pathPrefix = config.apiPath.substring(
              0, config.apiPath.length - chatCompletionsSuffix.length);

          // Ensure apiUrl doesn't end with '/' and pathPrefix doesn't start with '/' or is empty
          String trimmedApiUrl = config.apiUrl.endsWith('/')
              ? config.apiUrl.substring(0, config.apiUrl.length - 1)
              : config.apiUrl;
          String trimmedPathPrefix = pathPrefix.isEmpty
              ? '' // No prefix if path was just '/chat/completions'
              : (pathPrefix.startsWith('/') ? pathPrefix : '/$pathPrefix');

          effectiveBaseUrl = '$trimmedApiUrl$trimmedPathPrefix';
          // Debug print to verify the constructed base URL
          // print('Using effectiveBaseUrl for compatible API: $effectiveBaseUrl (from apiUrl: ${config.apiUrl}, apiPath: ${config.apiPath})');
        }
        // Add more conditions here if other specific non-standard apiPaths need handling differently
        // else {
        //   // If apiPath doesn't end with the expected suffix, treat apiUrl as the base
        //   effectiveBaseUrl = config.apiUrl;
        // }
      }
      // If apiPath is empty, we use config.apiUrl directly as the base.
      // This works for standard OpenAI (apiUrl=https://api.openai.com)
      // but will likely fail for non-compatible endpoints like ".../generation"
      // entered directly into apiUrl without a corresponding apiPath.

      // 检查是否是阿里云通义千问模型，如果是，使用专门的适配器
      bool isAliyunQwen = config.apiUrl.contains('dashscope.aliyuncs.com') ||
          config.name.contains('阿里') ||
          config.name.contains('通义');

      if (isAliyunQwen) {
        print('[ModelAdapter] 检测到阿里云通义千问模型，使用专门的AliyunQwenAdapter');

        // 使用专门的阿里云通义千问适配器
        final aliyunAdapter = AliyunQwenAdapter(
          apiKey: config.apiKey,
          baseUrl: effectiveBaseUrl,
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens > 0 ? config.maxTokens : null,
          topP: config.topP,
          extraHeaders: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          enableThinking: config.enableThinking, // 使用配置中的深度思考模式设置
        );

        // 将AliyunQwenAdapter转换为BaseChatModel类型
        return aliyunAdapter as BaseChatModel;
      } else {
        return ChatOpenAI(
          apiKey: config.apiKey,
          baseUrl: effectiveBaseUrl,
          defaultOptions: defaultOptions,
        );
      }
    }
  }
}

/// 自定义的Google API适配器，用于正确处理Google Gemini API调用
class GoogleAPIAdapter extends BaseChatModel {
  final ModelConfig config;
  final ChatOpenAIOptions defaultOptions;

  GoogleAPIAdapter({
    required this.config,
    required this.defaultOptions,
  });

  @override
  String get modelType => 'google-api';

  @override
  Future<LanguageModelResult<AIChatMessage>> invoke(
    PromptValue input, {
    ChatModelOptions? options,
  }) async {
    // 将流式调用的结果收集为单个响应
    final buffer = StringBuffer();
    final streamResult = stream(input, options: options);

    await for (final result in streamResult) {
      if (result.generations.isNotEmpty) {
        final generation = result.generations.first;
        final message = generation.output;
        buffer.write(message.contentAsString);
      }
    }

    final completeMessage = AIChatMessage(content: buffer.toString());
    return LanguageModelResult(generations: [ChatGeneration(completeMessage)]);
  }

  @override
  Stream<LanguageModelResult<AIChatMessage>> stream(
    PromptValue input, {
    ChatModelOptions? options,
  }) async* {
    try {
      // 将输入转换为字符串
      String inputText;
      if (input is ChatPromptValue) {
        // 合并所有消息为一个字符串
        inputText = input.messages.map((msg) => msg.contentAsString).join('\n');
      } else {
        inputText = input.toString();
      }

      // 使用AIService进行实际的API调用
      final aiService = Get.find<AIService>();

      await for (final chunk in aiService.generateTextStream(
        systemPrompt: '',
        userPrompt: inputText,
        temperature: defaultOptions.temperature ?? 0.7,
        maxTokens: defaultOptions.maxTokens,
        specificModelConfig: config,
      )) {
        if (chunk.isNotEmpty) {
          final message = AIChatMessage(content: chunk);
          yield LanguageModelResult(
            generations: [ChatGeneration(message)],
          );
        }
      }
    } catch (e) {
      print('GoogleAPIAdapter调用失败: $e');
      rethrow;
    }
  }

  @override
  Future<LanguageModelResult<AIChatMessage>> generate(
    List<ChatMessage> inputs, {
    ChatModelOptions? options,
  }) async {
    // 简单实现：将ChatMessage转换为PromptValue
    if (inputs.isEmpty) {
      return const LanguageModelResult(generations: <ChatGeneration>[]);
    }

    final promptValue = ChatPromptValue(inputs);
    final result = await invoke(promptValue, options: options);
    return result;
  }

  @override
  Future<List<int>> tokenize(
    PromptValue input, {
    ChatModelOptions? options,
  }) async {
    // 简单的tokenize实现：按字符分割
    final text = input.toString();
    return text.codeUnits;
  }
}

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;

/// 后台服务，用于保持应用在后台运行时不被系统杀死
class BackgroundService extends GetxService {
  // 方法通道
  static const MethodChannel _channel =
      MethodChannel('com.daizhong.novelapp/background_service');

  // 服务是否正在运行
  final RxBool isRunning = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 初始化时检查服务状态
    checkServiceStatus();
  }

  /// 检查服务状态
  Future<void> checkServiceStatus() async {
    // 只在Android平台执行
    if (kIsWeb || !Platform.isAndroid) {
      isRunning.value = false;
      return;
    }

    try {
      final bool running =
          await _channel.invokeMethod('isBackgroundServiceRunning');
      isRunning.value = running;
      print('后台服务状态: ${isRunning.value ? "运行中" : "未运行"}');
    } catch (e) {
      print('检查后台服务状态失败: $e');
      isRunning.value = false;
    }
  }

  /// 启动后台服务
  Future<bool> startService() async {
    // 只在Android平台执行
    if (kIsWeb || !Platform.isAndroid) {
      return false;
    }

    try {
      // 先检查服务是否已经在运行
      await checkServiceStatus();

      // 如果服务已经在运行，直接返回成功
      if (isRunning.value) {
        print('后台服务已经在运行，无需重复启动');
        return true;
      }

      // 启动服务
      final bool result = await _channel.invokeMethod('startBackgroundService');
      isRunning.value = result;
      print('启动后台服务${result ? "成功" : "失败"}');

      // 延迟检查服务状态，确保服务已经启动
      Future.delayed(const Duration(seconds: 1), () {
        checkServiceStatus();
      });

      return result;
    } catch (e) {
      print('启动后台服务失败: $e');
      isRunning.value = false;
      return false;
    }
  }

  /// 停止后台服务
  Future<bool> stopService() async {
    // 只在Android平台执行
    if (kIsWeb || !Platform.isAndroid) {
      return false;
    }

    try {
      final bool result = await _channel.invokeMethod('stopBackgroundService');
      isRunning.value = !result;
      print('停止后台服务${result ? "成功" : "失败"}');
      return result;
    } catch (e) {
      print('停止后台服务失败: $e');
      return false;
    }
  }
}

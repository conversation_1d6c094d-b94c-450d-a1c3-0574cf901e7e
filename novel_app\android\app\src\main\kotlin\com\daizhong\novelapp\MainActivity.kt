package com.daizhong.novelapp

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant
import android.os.Bundle
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.os.Environment
import android.os.PowerManager
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class MainActivity: FlutterActivity() {
    companion object {
        private const val PERMISSION_CHANNEL = "com.daizhong.novelapp/permissions"
        private const val UPDATE_CHANNEL = "com.daizhong.novelapp/update"
        private const val UPDATE_EVENT_CHANNEL = "com.daizhong.novelapp/update/events"
        private const val BACKGROUND_SERVICE_CHANNEL = "com.daizhong.novelapp/background_service"
        private const val PERMISSION_REQUEST_CODE = 123
        private const val TAG = "MainActivity"
    }

    private var permissionResult: MethodChannel.Result? = null
    private lateinit var appUpdateManager: AppUpdateManager
    private var updateEventSink: EventChannel.EventSink? = null
    private lateinit var updateMethodChannel: MethodChannel
    private lateinit var permissionMethodChannel: MethodChannel
    private lateinit var backgroundServiceMethodChannel: MethodChannel
    private lateinit var updateEventChannel: EventChannel

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        GeneratedPluginRegistrant.registerWith(flutterEngine)

        // 初始化更新管理器
        appUpdateManager = AppUpdateManager(this)

        // 打印调试信息
        Log.d(TAG, "配置Flutter引擎，设置更新通道")

        try {
            // 设置更新事件通道
            updateEventChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, UPDATE_EVENT_CHANNEL)
            updateEventChannel.setStreamHandler(
                object : EventChannel.StreamHandler {
                    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                        Log.d(TAG, "事件通道onListen被调用")
                        updateEventSink = events
                    }

                    override fun onCancel(arguments: Any?) {
                        Log.d(TAG, "事件通道onCancel被调用")
                        updateEventSink = null
                    }
                }
            )
            Log.d(TAG, "更新事件通道设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置更新事件通道失败", e)
        }

        try {
            // 权限通道
            permissionMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, PERMISSION_CHANNEL)
            permissionMethodChannel.setMethodCallHandler { call, result ->
                when (call.method) {
                    "requestPermissions" -> {
                        permissionResult = result
                        checkAndRequestPermissions()
                    }
                    "requestBatteryOptimization" -> {
                        requestBatteryOptimization()
                        result.success(true)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
            Log.d(TAG, "权限通道设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置权限通道失败", e)
        }

        try {
            // 后台服务通道
            backgroundServiceMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, BACKGROUND_SERVICE_CHANNEL)
            backgroundServiceMethodChannel.setMethodCallHandler { call, result ->
                Log.d(TAG, "收到后台服务方法调用: ${call.method}")

                try {
                    when (call.method) {
                        "startBackgroundService" -> {
                            Log.d(TAG, "启动后台服务")
                            // 启动后台服务
                            BackgroundService.startService(this)
                            result.success(true)

                            // 延迟请求忽略电池优化，避免立即启动新Activity导致闪退
                            Handler(Looper.getMainLooper()).postDelayed({
                                requestBatteryOptimization()
                            }, 1000)
                        }
                        "stopBackgroundService" -> {
                            Log.d(TAG, "停止后台服务")
                            // 停止后台服务
                            BackgroundService.stopService(this)
                            result.success(true)
                        }
                        "isBackgroundServiceRunning" -> {
                            val isRunning = BackgroundService.isRunning()
                            Log.d(TAG, "后台服务运行状态: $isRunning")
                            result.success(isRunning)
                        }
                        else -> {
                            Log.e(TAG, "未实现的后台服务方法: ${call.method}")
                            result.notImplemented()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "后台服务方法通道处理异常", e)
                    result.error("CHANNEL_ERROR", "后台服务方法通道处理异常: ${e.message}", null)
                }
            }
            Log.d(TAG, "后台服务通道设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置后台服务通道失败", e)
        }

        try {
            // 更新通道
            updateMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, UPDATE_CHANNEL)
            updateMethodChannel.setMethodCallHandler { call, result ->
                Log.d(TAG, "收到方法调用: ${call.method}")

                try {
                    when (call.method) {
                        "checkForUpdates" -> {
                            val updateUrl = call.argument<String>("updateUrl") ?: "https://dzwm.xyz/api/version.json"
                            val backupUpdateUrl = call.argument<String>("backupUpdateUrl")
                            val currentVersion = call.argument<String>("currentVersion") ?: "1.0.0"
                            Log.d(TAG, "检查更新: url=$updateUrl, backupUrl=$backupUpdateUrl, currentVersion=$currentVersion")

                            appUpdateManager.checkForUpdates(updateUrl, backupUpdateUrl, currentVersion) { updateResult ->
                                Log.d(TAG, "检查更新结果: $updateResult")
                                result.success(updateResult)
                            }
                        }
                        "downloadUpdate" -> {
                            Log.d(TAG, "downloadUpdate方法被调用")
                            val downloadUrl = call.argument<String>("downloadUrl")
                            val version = call.argument<String>("version")

                            if (downloadUrl.isNullOrEmpty() || version.isNullOrEmpty()) {
                                Log.e(TAG, "下载参数无效: url=$downloadUrl, version=$version")
                                result.error("INVALID_ARGUMENTS", "下载URL和版本号不能为空", null)
                                return@setMethodCallHandler
                            }

                            Log.d(TAG, "开始下载更新: url=$downloadUrl, version=$version")

                            // 先检查所有必要的权限
                            // 对于下载和安装APK，我们只需要确保有安装权限
                            // 存储权限在Android 10+已经不再需要单独请求，因为我们使用应用专用目录
                            val hasStoragePermission = true

                            val hasInstallPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                packageManager.canRequestPackageInstalls()
                            } else {
                                true
                            }

                            Log.d(TAG, "权限状态 - 存储: $hasStoragePermission, 安装: $hasInstallPermission")

                            if (!hasStoragePermission || !hasInstallPermission) {
                                // 显示权限请求对话框
                                val builder = android.app.AlertDialog.Builder(this)
                                builder.setTitle("需要权限")
                                    .setMessage("应用需要存储权限和安装权限来完成更新。请授予所需权限。")
                                    .setPositiveButton("授予权限") { _, _ ->
                                        // 请求权限
                                        checkAndRequestPermissions()

                                        // 返回错误，让用户在授予权限后重试
                                        result.error("PERMISSION_REQUIRED", "需要授予权限后重试", null)
                                    }
                                    .setNegativeButton("取消") { dialog, _ ->
                                        dialog.dismiss()
                                        result.error("PERMISSION_DENIED", "用户拒绝授予权限", null)
                                    }
                                    .show()
                                return@setMethodCallHandler
                            }

                            try {
                                // 检查事件接收器是否已设置
                                if (updateEventSink == null) {
                                    Log.w(TAG, "事件接收器为null，尝试重新设置事件通道")

                                    // 重新设置事件通道
                                    updateEventChannel.setStreamHandler(
                                        object : EventChannel.StreamHandler {
                                            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                                                Log.d(TAG, "事件通道onListen被重新调用")
                                                updateEventSink = events
                                            }

                                            override fun onCancel(arguments: Any?) {
                                                Log.d(TAG, "事件通道onCancel被重新调用")
                                                updateEventSink = null
                                            }
                                        }
                                    )

                                    // 等待一段时间，确保事件通道已设置
                                    Thread.sleep(500)

                                    // 再次检查事件接收器
                                    if (updateEventSink == null) {
                                        Log.w(TAG, "重新设置事件通道后，事件接收器仍为null，但将继续尝试下载")
                                    } else {
                                        Log.d(TAG, "事件接收器已重新设置")
                                    }
                                }

                                // 返回初始结果，后续进度通过事件通道发送
                                result.success(mapOf("status" to "started"))
                                Log.d(TAG, "已返回初始结果")

                                // 开始下载，使用全局事件接收器
                                appUpdateManager.downloadUpdate(downloadUrl, version) { progressResult ->
                                    Log.d(TAG, "下载进度更新: ${progressResult["progress"]}, 状态: ${progressResult["status"]}")
                                    runOnUiThread {
                                        if (updateEventSink != null) {
                                            try {
                                                updateEventSink?.success(progressResult)
                                                Log.d(TAG, "已通过事件通道发送进度")
                                            } catch (e: Exception) {
                                                Log.e(TAG, "发送进度时发生异常", e)
                                            }
                                        } else {
                                            Log.e(TAG, "事件接收器为null，无法发送进度")
                                            // 尝试通过Toast显示进度
                                            val progress = progressResult["progress"] as? Int ?: 0
                                            val status = progressResult["status"] as? String ?: "downloading"
                                            if (status == "downloading" && progress % 10 == 0) {
                                                Toast.makeText(this@MainActivity, "下载进度: $progress%", Toast.LENGTH_SHORT).show()
                                            } else if (status == "completed") {
                                                Toast.makeText(this@MainActivity, "下载完成，准备安装", Toast.LENGTH_LONG).show()
                                            } else if (status == "error") {
                                                val error = progressResult["error"] as? String ?: "未知错误"
                                                Toast.makeText(this@MainActivity, "下载失败: $error", Toast.LENGTH_LONG).show()
                                            }
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "下载过程异常", e)
                                result.error("DOWNLOAD_ERROR", "下载过程发生异常: ${e.message}", null)
                            }
                        }
                        "cancelDownload" -> {
                            Log.d(TAG, "取消下载")
                            appUpdateManager.cancelDownload()
                            result.success(true)
                        }
                        else -> {
                            Log.e(TAG, "未实现的方法: ${call.method}")
                            result.notImplemented()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "方法通道处理异常", e)
                    result.error("CHANNEL_ERROR", "方法通道处理异常: ${e.message}", null)
                }
            }
            Log.d(TAG, "更新通道设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置更新通道失败", e)
        }
    }



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建通知渠道
        createNotificationChannel()

        // 初始权限检查
        checkAndRequestPermissions()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "novel_app_service"
            val channelName = "岱宗文脉服务"
            val channel = NotificationChannel(
                channelId,
                channelName,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "保持应用在后台运行"
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun requestBatteryOptimization() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val packageName = packageName
            val pm = getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                try {
                    // 显示对话框询问用户是否要打开电池优化设置
                    val builder = android.app.AlertDialog.Builder(this)
                    builder.setTitle("需要忽略电池优化")
                        .setMessage("为了确保应用在后台运行时不被系统杀死，需要将应用添加到电池优化白名单中。是否现在设置？")
                        .setPositiveButton("设置") { _, _ ->
                            try {
                                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                                    data = Uri.parse("package:$packageName")
                                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                }
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "无法打开电池优化设置", e)
                                Toast.makeText(this, "无法打开电池优化设置", Toast.LENGTH_SHORT).show()
                            }
                        }
                        .setNegativeButton("稍后再说") { dialog, _ ->
                            dialog.dismiss()
                        }
                        .show()
                } catch (e: Exception) {
                    Log.e(TAG, "请求电池优化异常", e)
                }
            }
        }
    }

    private fun checkAndRequestPermissions() {
        Log.d("MainActivity", "检查权限")

        val permissions = mutableListOf<String>()

        // 根据Android版本选择合适的权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
            permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
            Log.d("MainActivity", "Android 13+，请求媒体权限")
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11-12
            // 这些版本需要MANAGE_EXTERNAL_STORAGE权限，但这个权限需要通过特殊方式请求
            // 先请求基本存储权限
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            Log.d("MainActivity", "Android 11-12，请求基本存储权限，稍后会请求MANAGE_EXTERNAL_STORAGE")
        } else {
            // Android 10及以下
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            Log.d("MainActivity", "Android 10及以下，请求基本存储权限")
        }

        val permissionsToRequest = mutableListOf<String>()

        // 检查每个权限
        for (permission in permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission)
                Log.d("MainActivity", "需要请求权限: $permission")
            } else {
                Log.d("MainActivity", "已授予权限: $permission")
            }
        }

        // 如果有需要请求的权限
        if (permissionsToRequest.isNotEmpty()) {
            Log.d("MainActivity", "请求权限: ${permissionsToRequest.joinToString()}")

            // 显示权限请求对话框
            ActivityCompat.requestPermissions(
                this,
                permissionsToRequest.toTypedArray(),
                PERMISSION_REQUEST_CODE
            )
        } else {
            // 如果基本权限都已授予，检查特殊权限
            Log.d("MainActivity", "基本权限已授予，检查特殊权限")

            // 检查特殊权限
            val needSpecialPermissions = checkSpecialPermissions()

            if (!needSpecialPermissions && permissionResult != null) {
                Log.d("MainActivity", "所有权限已授予")
                val result = permissionResult
                permissionResult = null // 清空引用，防止重复回复
                result?.success(true)
            }
        }
    }

    /**
     * 检查特殊权限（需要通过Intent请求的权限）
     * @return 是否需要请求特殊权限
     */
    private fun checkSpecialPermissions(): Boolean {
        var needSpecialPermissions = false

        // 我们使用应用专用目录，不需要MANAGE_EXTERNAL_STORAGE权限
        Log.d("MainActivity", "使用应用专用目录，不需要MANAGE_EXTERNAL_STORAGE权限")

        // 检查REQUEST_INSTALL_PACKAGES权限（Android 8+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!packageManager.canRequestPackageInstalls()) {
                Log.d("MainActivity", "需要请求REQUEST_INSTALL_PACKAGES权限")
                checkInstallPackagesPermission()
                needSpecialPermissions = true
            } else {
                Log.d("MainActivity", "已授予REQUEST_INSTALL_PACKAGES权限")
            }
        }

        return needSpecialPermissions
    }

    private fun checkInstallPackagesPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!packageManager.canRequestPackageInstalls()) {
                Log.d("MainActivity", "请求安装未知来源应用的权限")

                // 显示解释对话框
                val builder = android.app.AlertDialog.Builder(this)
                builder.setTitle("需要安装权限")
                    .setMessage("应用需要安装未知来源应用的权限来完成更新。请在接下来的设置页面中点击允许来自此来源的应用开关来授予权限。")
                    .setPositiveButton("去设置") { _, _ ->
                        try {
                            val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                                data = Uri.parse("package:$packageName")
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            }

                            // 使用传统的startActivityForResult
                            startActivityForResult(intent, 1001)
                        } catch (e: Exception) {
                            Log.e("MainActivity", "无法打开安装未知应用权限设置", e)
                            Toast.makeText(this, "无法打开安装权限设置，请手动授予权限", Toast.LENGTH_LONG).show()
                            if (permissionResult != null) {
                                val result = permissionResult
                                permissionResult = null // 清空引用，防止重复回复
                                result?.error("PERMISSION_DENIED", "无法打开安装权限设置", null)
                            }
                        }
                    }
                    .setNegativeButton("取消") { dialog, _ ->
                        dialog.dismiss()
                        Toast.makeText(this, "未授予安装权限，更新功能将无法正常工作", Toast.LENGTH_LONG).show()
                        if (permissionResult != null) {
                            val result = permissionResult
                            permissionResult = null // 清空引用，防止重复回复
                            result?.error("PERMISSION_DENIED", "用户拒绝授予安装权限", null)
                        }
                    }
                    .show()
            } else {
                Log.d("MainActivity", "已有安装未知来源应用的权限")
                // 所有权限都已授予，通知Flutter端
                if (permissionResult != null) {
                    val result = permissionResult
                    permissionResult = null // 清空引用，防止重复回复
                    result?.success(true)
                }
            }
        } else {
            // Android 8以下不需要此权限
            if (permissionResult != null) {
                val result = permissionResult
                permissionResult = null // 清空引用，防止重复回复
                result?.success(true)
            }
        }
    }

    private fun checkManageExternalStoragePermission() {
        // 我们使用应用专用目录，不需要MANAGE_EXTERNAL_STORAGE权限
        Log.d("MainActivity", "使用应用专用目录，不需要MANAGE_EXTERNAL_STORAGE权限")

        // 继续检查安装权限
        checkInstallPackagesPermission()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            // 记录每个权限的授予状态
            val permissionResults = mutableMapOf<String, Boolean>()
            var allGranted = true

            for (i in permissions.indices) {
                val permission = permissions[i]
                val result = grantResults[i]
                val isGranted = result == PackageManager.PERMISSION_GRANTED

                permissionResults[permission] = isGranted
                Log.d("MainActivity", "权限 $permission: ${if (isGranted) "已授予" else "被拒绝"}")

                if (!isGranted) {
                    allGranted = false
                }
            }

            if (!allGranted) {
                Log.d("MainActivity", "部分权限被拒绝")

                // 检查是否有永久拒绝的权限
                var hasPermanentlyDenied = false
                for (permission in permissions) {
                    if (!permissionResults[permission]!! && !shouldShowRequestPermissionRationale(permission)) {
                        hasPermanentlyDenied = true
                        Log.d("MainActivity", "权限 $permission 被永久拒绝")
                        break
                    }
                }

                if (hasPermanentlyDenied) {
                    // 如果有永久拒绝的权限，显示引导用户去设置页面的对话框
                    showSettingsPermissionDialog()
                } else {
                    // 如果只是临时拒绝，显示解释对话框
                    showPermissionExplanationDialog()
                }

                // 通知Flutter端权限被拒绝
                if (permissionResult != null) {
                    val result = permissionResult
                    permissionResult = null // 清空引用，防止重复回复
                    result?.error("PERMISSION_DENIED", "存储权限被拒绝", null)
                }
            } else {
                Log.d("MainActivity", "所有基本权限都已授予，继续检查特殊权限")

                // 继续检查特殊权限
                val needSpecialPermissions = checkSpecialPermissions()

                if (!needSpecialPermissions && permissionResult != null) {
                    Log.d("MainActivity", "所有权限都已授予")
                    val result = permissionResult
                    permissionResult = null // 清空引用，防止重复回复
                    result?.success(true)
                }
            }
        }
    }

    /**
     * 显示引导用户去设置页面的权限对话框
     */
    private fun showSettingsPermissionDialog() {
        val builder = android.app.AlertDialog.Builder(this)
        builder.setTitle("需要在设置中授予权限")
            .setMessage("应用需要存储权限来正常工作，但您已拒绝并不再询问。请在设置中手动授予权限。")
            .setPositiveButton("去设置") { _, _ ->
                try {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(intent)
                } catch (e: Exception) {
                    Toast.makeText(this, "无法打开应用设置", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
                Toast.makeText(this, "未授予必要权限，应用功能将受限", Toast.LENGTH_LONG).show()
            }
            .show()
    }

    private fun showPermissionExplanationDialog() {
        val builder = android.app.AlertDialog.Builder(this)
        builder.setTitle("需要存储权限")
            .setMessage("应用需要存储权限来保存您的创作内容。请在设置中授予权限。")
            .setPositiveButton("去设置") { _, _ ->
                try {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(intent)
                } catch (e: Exception) {
                    Toast.makeText(this, "无法打开应用设置", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            1001 -> {
                // 安装权限请求结果
                if (packageManager.canRequestPackageInstalls()) {
                    Log.d("MainActivity", "已成功授予安装权限")
                    Toast.makeText(this, "已授予安装权限", Toast.LENGTH_SHORT).show()

                    // 所有权限都已授予，通知Flutter端
                    if (permissionResult != null) {
                        val result = permissionResult
                        permissionResult = null // 清空引用，防止重复回复
                        result?.success(true)
                    }
                } else {
                    Log.d("MainActivity", "安装权限请求被拒绝")
                    Toast.makeText(this, "未授予安装权限，更新功能将无法正常工作", Toast.LENGTH_LONG).show()
                    if (permissionResult != null) {
                        val result = permissionResult
                        permissionResult = null // 清空引用，防止重复回复
                        result?.error("PERMISSION_DENIED", "安装权限被拒绝", null)
                    }
                }
            }
            1002 -> {
                // 文件管理权限请求结果 - 我们不再需要这个权限
                Log.d("MainActivity", "使用应用专用目录，不需要文件管理权限")

                // 继续检查安装权限
                checkInstallPackagesPermission()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理更新管理器资源
        if (::appUpdateManager.isInitialized) {
            appUpdateManager.cleanup()
        }
    }
}
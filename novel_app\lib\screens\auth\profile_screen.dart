import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/auth_controller.dart';
import 'package:novel_app/models/user.dart';

class ProfileScreen extends StatelessWidget {
  final _authController = Get.find<AuthController>();

  ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
      ),
      body: Obx(() {
        final user = _authController.currentUser.value;
        
        if (user == null) {
          return const Center(
            child: Text('请先登录'),
          );
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildUserInfoCard(user),
              const SizedBox(height: 24),
              _buildActionButtons(),
            ],
          ),
        );
      }),
    );
  }
  
  Widget _buildUserInfoCard(User user) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: CircleAvatar(
                radius: 50,
                backgroundColor: Colors.blue.shade100,
                child: Text(
                  user.username.isNotEmpty ? user.username[0].toUpperCase() : '?',
                  style: const TextStyle(fontSize: 40, color: Colors.blue),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.person, '用户名', user.username),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.email, '邮箱', user.email),
            const SizedBox(height: 8),
            _buildInfoRow(
              Icons.calendar_today, 
              '注册时间', 
              user.createdAt != null 
                ? '${user.createdAt!.year}-${user.createdAt!.month}-${user.createdAt!.day}'
                : '未知'
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 16),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
  
  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        FilledButton.icon(
          onPressed: () {
            // 暂时不实现修改密码功能
            Get.snackbar('提示', '修改密码功能暂未实现');
          },
          icon: const Icon(Icons.lock_reset),
          label: const Text('修改密码'),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: () {
            Get.dialog(
              AlertDialog(
                title: const Text('退出登录'),
                content: const Text('确定要退出登录吗？'),
                actions: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                  FilledButton(
                    onPressed: () {
                      Get.back();
                      _authController.logout();
                    },
                    child: const Text('确定'),
                  ),
                ],
              ),
            );
          },
          icon: const Icon(Icons.logout),
          label: const Text('退出登录'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
          ),
        ),
      ],
    );
  }
}

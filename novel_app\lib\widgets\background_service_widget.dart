import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/background_service.dart';

/// 后台服务状态和控制小部件
class BackgroundServiceWidget extends StatefulWidget {
  const BackgroundServiceWidget({Key? key}) : super(key: key);

  @override
  State<BackgroundServiceWidget> createState() =>
      _BackgroundServiceWidgetState();
}

class _BackgroundServiceWidgetState extends State<BackgroundServiceWidget> {
  // 是否正在处理操作
  final RxBool _isProcessing = false.obs;

  // 获取后台服务，如果不存在则注册
  BackgroundService get service {
    try {
      return Get.find<BackgroundService>();
    } catch (e) {
      // 如果服务未注册，则注册并返回
      print('后台服务未注册，正在自动注册');
      return Get.put(BackgroundService());
    }
  }

  @override
  void initState() {
    super.initState();
    // 初始化时检查服务状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // 确保服务已初始化
        final svc = service;
        svc.checkServiceStatus();
      } catch (e) {
        print('检查后台服务状态失败: $e');
      }
    });
  }

  // 启动服务
  Future<void> _startService() async {
    if (_isProcessing.value) return;

    try {
      _isProcessing.value = true;

      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在启动后台服务...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 启动服务
      final result = await service.startService();

      // 关闭对话框
      Get.back();

      // 显示结果
      if (result) {
        Get.snackbar(
          '成功',
          '后台服务已启动',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '失败',
          '后台服务启动失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // 关闭对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示错误
      Get.snackbar(
        '错误',
        '启动后台服务时发生错误: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isProcessing.value = false;
    }
  }

  // 停止服务
  Future<void> _stopService() async {
    if (_isProcessing.value) return;

    try {
      _isProcessing.value = true;

      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在停止后台服务...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 停止服务
      final result = await service.stopService();

      // 关闭对话框
      Get.back();

      // 显示结果
      if (result) {
        Get.snackbar(
          '成功',
          '后台服务已停止',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '失败',
          '后台服务停止失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // 关闭对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示错误
      Get.snackbar(
        '错误',
        '停止后台服务时发生错误: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isProcessing.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 确保服务已初始化
    final BackgroundService svc = service;

    return Obx(() {
      final bool isRunning = svc.isRunning.value;
      final bool isProcessing = _isProcessing.value;

      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    isRunning ? Icons.check_circle : Icons.error,
                    color: isRunning ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '后台服务状态: ${isRunning ? "运行中" : "未运行"}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: isProcessing
                        ? null
                        : () => service.checkServiceStatus(),
                    tooltip: '刷新状态',
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '后台服务可以保持应用在后台运行时不被系统杀死，确保小说生成不会中断。（仅针对安卓）',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: isProcessing
                        ? null
                        : (isRunning ? _stopService : _startService),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isRunning ? Colors.red : Colors.green,
                      foregroundColor: Colors.white,
                      disabledBackgroundColor: Colors.grey,
                    ),
                    child: Text(isProcessing
                        ? '处理中...'
                        : (isRunning ? '停止服务' : '启动服务')),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}

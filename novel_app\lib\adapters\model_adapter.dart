import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:novel_app/models/model_config.dart';

/// 模型适配器，用于根据配置创建不同的LLM实例
class ModelAdapter {
  /// 根据模型配置创建LLM实例
  static BaseChatModel createLLMFromConfig(ModelConfig config) {
    print('[ModelAdapter] 创建LLM，API格式: ${config.apiFormat}');

    // 检查API格式，支持OpenAI兼容和Google API
    if (config.apiFormat == 'OpenAI API兼容' || config.apiFormat == 'openai') {
      return ChatOpenAI(
        apiKey: config.apiKey,
        baseUrl: config.apiUrl,
        defaultOptions: ChatOpenAIOptions(
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
        ),
      );
    } else if (config.apiFormat == 'Google API' ||
        config.apiFormat == 'google') {
      // 对于Google API，我们暂时使用OpenAI兼容的方式，因为实际的API调用在AIService中处理
      return ChatOpenAI(
        apiKey: config.apiKey,
        baseUrl: config.apiUrl,
        defaultOptions: ChatOpenAIOptions(
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
        ),
      );
    } else {
      // 默认使用OpenAI兼容格式
      print('[ModelAdapter] 未知API格式 ${config.apiFormat}，使用OpenAI兼容格式');
      return ChatOpenAI(
        apiKey: config.apiKey,
        baseUrl: config.apiUrl,
        defaultOptions: ChatOpenAIOptions(
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
        ),
      );
    }
  }
}

import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart'
    show
        rootBundle,
        MethodChannel,
        EventChannel,
        PlatformException,
        MissingPluginException;
import 'package:novel_app/services/windows_update_service.dart';

/// 版本信息模型
class VersionInfo {
  final String version;
  final String buildNumber;
  final String releaseDate;
  final String downloadUrl;
  String platformSpecificUrl; // 平台特定的下载URL
  final String releaseNotes;
  final bool forceUpdate;

  VersionInfo({
    required this.version,
    required this.buildNumber,
    required this.downloadUrl,
    required this.releaseNotes,
    this.releaseDate = '',
    this.forceUpdate = false,
    String? platformSpecificUrl,
  }) : platformSpecificUrl = platformSpecificUrl ?? downloadUrl {
    // 如果没有提供平台特定URL，根据平台自动设置
    if (platformSpecificUrl == null) {
      this.platformSpecificUrl = _getPlatformSpecificUrl(downloadUrl);
    }
  }

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    final String downloadUrl = json['downloadUrl'] ?? '';
    String? platformUrl;

    // 在非Web平台上检查平台特定URL
    if (!kIsWeb) {
      if (Platform.isWindows && json['windowsDownloadUrl'] != null) {
        platformUrl = json['windowsDownloadUrl'];
      } else if (Platform.isAndroid && json['androidDownloadUrl'] != null) {
        platformUrl = json['androidDownloadUrl'];
      }
    }

    return VersionInfo(
      version: json['version'] ?? '',
      buildNumber: json['buildNumber'] ?? '',
      releaseDate: json['releaseDate'] ?? '',
      downloadUrl: downloadUrl,
      releaseNotes: json['releaseNotes'] ?? '',
      forceUpdate: json['forceUpdate'] ?? false,
      platformSpecificUrl: platformUrl,
    );
  }

  // 获取平台特定的下载URL
  static String _getPlatformSpecificUrl(String url) {
    // Web平台直接返回原URL
    if (kIsWeb) {
      return url;
    }

    // 非Web平台根据操作系统类型处理
    if (Platform.isWindows) {
      // 确保Windows平台使用.exe文件
      if (!url.toLowerCase().endsWith('.exe')) {
        // 如果URL不是以.exe结尾，尝试替换扩展名或添加.exe
        if (url.toLowerCase().endsWith('.apk')) {
          // 替换.apk为.exe
          return url.replaceAll(
              RegExp(r'\.apk$', caseSensitive: false), '.exe');
        } else {
          // 如果没有明确的扩展名，添加_windows后缀
          return '${url}_windows.exe';
        }
      }
    } else if (Platform.isAndroid) {
      // 确保Android平台使用.apk文件
      if (!url.toLowerCase().endsWith('.apk')) {
        // 如果URL不是以.apk结尾，尝试替换扩展名或添加.apk
        if (url.toLowerCase().endsWith('.exe')) {
          // 替换.exe为.apk
          return url.replaceAll(
              RegExp(r'\.exe$', caseSensitive: false), '.apk');
        } else {
          // 如果没有明确的扩展名，添加_android后缀
          return '${url}_android.apk';
        }
      }
    }
    // 如果已经是正确的扩展名或其他平台，直接返回原URL
    return url;
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildNumber': buildNumber,
      'releaseDate': releaseDate,
      'downloadUrl': downloadUrl,
      'platformSpecificUrl': platformSpecificUrl,
      'releaseNotes': releaseNotes,
      'forceUpdate': forceUpdate,
    };
  }

  /// 比较版本号
  bool isNewerThan(String currentVersion, String currentBuildNumber) {
    // 首先比较主版本号
    final List<int> currentVersionParts = currentVersion
        .split('.')
        .map((part) => int.tryParse(part) ?? 0)
        .toList();

    final List<int> newVersionParts =
        version.split('.').map((part) => int.tryParse(part) ?? 0).toList();

    // 确保两个列表长度相同
    while (currentVersionParts.length < newVersionParts.length) {
      currentVersionParts.add(0);
    }
    while (newVersionParts.length < currentVersionParts.length) {
      newVersionParts.add(0);
    }

    // 比较版本号的每一部分
    for (int i = 0; i < newVersionParts.length; i++) {
      if (newVersionParts[i] > currentVersionParts[i]) {
        return true;
      } else if (newVersionParts[i] < currentVersionParts[i]) {
        return false;
      }
    }

    // 如果版本号相同，比较构建号
    final int currentBuild = int.tryParse(currentBuildNumber) ?? 0;
    final int newBuild = int.tryParse(buildNumber) ?? 0;

    return newBuild > currentBuild;
  }
}

class UpdateService extends GetxService {
  static const String _lastCheckTimeKey = 'last_update_check_time';
  static const String _ignoreVersionKey = 'ignore_update_version';
  static const Duration _checkInterval = Duration(hours: 12); // 检查间隔时间

  final updateServerUrl = 'http://47.120.19.139/api/version.json'; // 主要更新服务器URL
  final backupUpdateServerUrl =
      'http://47.120.19.139/api/version.json'; // 备用更新服务器URL
  final bool _useLocalTestData = false; // 使用服务器数据
  final Rxn<VersionInfo> latestVersion = Rxn<VersionInfo>();
  final RxBool isCheckingUpdate = false.obs;
  final RxBool updateAvailable = false.obs;
  final RxBool isDownloading = false.obs;
  final RxInt downloadProgress = 0.obs;
  final RxString downloadStatus = ''.obs;

  // 原生更新通道
  static const MethodChannel _updateChannel = MethodChannel(
    'com.daizhong.novelapp/update',
  );
  static const EventChannel _updateEventChannel = EventChannel(
    'com.daizhong.novelapp/update/events',
  );

  // 是否使用原生更新功能（仅Android平台）
  final bool _useNativeUpdate = !kIsWeb && Platform.isAndroid;

  // Windows平台更新服务
  final WindowsUpdateService _windowsUpdateService = WindowsUpdateService();

  late final SharedPreferences _prefs;

  @override
  void onInit() {
    super.onInit();
    _initPrefs();

    // 监听更新事件（仅在Android平台）
    if (!kIsWeb && Platform.isAndroid) {
      // 延迟设置更新事件监听，确保Flutter引擎完全初始化
      Future.delayed(const Duration(seconds: 1), () {
        _setupUpdateEventListener();
      });
    }
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
    // 应用启动时自动检查更新
    checkForUpdates(showLoading: false);
  }

  /// 设置更新事件监听
  void _setupUpdateEventListener() {
    print('设置更新事件监听');
    try {
      _updateEventChannel.receiveBroadcastStream().listen(
        (event) {
          print('收到更新事件: $event');
          if (event is Map) {
            final Map<String, dynamic> data = Map<String, dynamic>.from(event);
            final String status = data['status'] ?? '';

            print('更新状态: $status, 数据: $data');
            downloadStatus.value = status;

            if (status == 'downloading') {
              isDownloading.value = true;
              downloadProgress.value = data['progress'] ?? 0;
              print('下载进度: ${downloadProgress.value}%');

              // 如果有消息，显示给用户
              if (data.containsKey('message')) {
                print('下载消息: ${data['message']}');
                Get.snackbar('下载提示', data['message']);
              }
            } else if (status == 'completed') {
              isDownloading.value = false;
              downloadProgress.value = 100;
              print('下载完成，准备安装');
              Get.snackbar('下载完成', '安装包已下载完成，正在安装...');

              // 确保进度条显示100%
              Future.delayed(const Duration(milliseconds: 500), () {
                downloadProgress.value = 100;
              });
            } else if (status == 'installing') {
              isDownloading.value = false;
              downloadProgress.value = 100;
              print('正在安装: ${data['message'] ?? ""}');
              Get.snackbar('安装提示', data['message'] ?? '正在打开安装界面，请点击"安装"');
            } else if (status == 'error') {
              isDownloading.value = false;
              print('下载错误: ${data['error']}');
              Get.snackbar('下载失败', data['error'] ?? '未知错误');
            }
          } else {
            print('收到非Map类型事件: $event');
          }
        },
        onError: (error) {
          print('事件通道错误: $error');
          isDownloading.value = false;
          Get.snackbar('更新错误', '监听更新事件失败: $error');
        },
        onDone: () {
          print('事件通道关闭');
        },
        cancelOnError: false,
      );
      print('事件监听器设置完成');
    } catch (e) {
      print('设置事件监听器失败: $e');
      Get.snackbar('更新错误', '设置事件监听器失败: $e');
    }
  }

  /// 检查更新
  Future<void> checkForUpdates({
    bool showLoading = true,
    bool forceCheck = false,
  }) async {
    try {
      // 如果正在检查，直接返回
      if (isCheckingUpdate.value) return;

      isCheckingUpdate.value = true;
      if (showLoading) {
        Get.dialog(
          const Center(child: CircularProgressIndicator()),
          barrierDismissible: false,
        );
      }

      // 检查是否需要检查更新
      if (!forceCheck && !_shouldCheckForUpdates()) {
        isCheckingUpdate.value = false;
        if (showLoading) Get.back();
        return;
      }

      // 获取当前应用版本信息
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentBuildNumber = packageInfo.buildNumber;

      // 获取最新版本信息
      VersionInfo newVersion;

      try {
        // 如果是Android平台且启用了原生更新
        if (Platform.isAndroid && _useNativeUpdate) {
          final result = await _checkNativeUpdate(currentVersion);

          if (result != null) {
            // 创建VersionInfo对象
            newVersion = VersionInfo(
              version: result['version'] ?? '',
              buildNumber: result['buildNumber'] ?? '1',
              downloadUrl: result['downloadUrl'] ?? '',
              releaseNotes: result['releaseNotes'] ?? '',
              releaseDate: result['releaseDate'] ?? '',
              forceUpdate: result['forceUpdate'] ?? false,
            );

            // 保存最新版本信息
            latestVersion.value = newVersion;

            // 保存最后检查时间
            _prefs.setInt(
              _lastCheckTimeKey,
              DateTime.now().millisecondsSinceEpoch,
            );

            // 检查是否有新版本
            final bool hasUpdate = result['hasUpdate'] ?? false;
            final String ignoredVersion =
                _prefs.getString(_ignoreVersionKey) ?? '';
            final bool isIgnored = ignoredVersion == newVersion.version;

            if (hasUpdate && !isIgnored) {
              updateAvailable.value = true;

              // 如果显示加载对话框，先关闭它
              if (showLoading) Get.back();

              // 显示更新对话框
              _showUpdateDialog(newVersion);
            } else {
              updateAvailable.value = false;
              if (showLoading) {
                Get.back();
                Get.snackbar('检查更新', '您当前使用的已经是最新版本');
              }
            }

            return;
          }
        }

        // 如果原生更新失败或不是Android平台，使用传统方式
        if (_useLocalTestData) {
          // 从本地资源加载测试数据
          final jsonString = await rootBundle.loadString('assets/version.json');
          final Map<String, dynamic> data = json.decode(jsonString);
          newVersion = VersionInfo.fromJson(data);
        } else {
          // 从服务器获取版本信息
          try {
            // 首先尝试主要URL
            final response = await http
                .get(Uri.parse(updateServerUrl))
                .timeout(const Duration(seconds: 10));

            if (response.statusCode == 200) {
              final Map<String, dynamic> data = json.decode(response.body);
              newVersion = VersionInfo.fromJson(data);
            } else {
              throw Exception('主服务器响应错误: ${response.statusCode}');
            }
          } catch (e) {
            print('主服务器请求失败，尝试备用服务器: $e');

            // 如果主要URL失败，尝试备用URL
            final response = await http
                .get(Uri.parse(backupUpdateServerUrl))
                .timeout(const Duration(seconds: 10));

            if (response.statusCode != 200) {
              throw Exception('备用服务器响应错误: ${response.statusCode}');
            }

            final Map<String, dynamic> data = json.decode(response.body);
            newVersion = VersionInfo.fromJson(data);
          }
        }

        // 保存最新版本信息
        latestVersion.value = newVersion;

        // 保存最后检查时间
        _prefs.setInt(_lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);

        // 检查是否有新版本
        final String ignoredVersion = _prefs.getString(_ignoreVersionKey) ?? '';
        final bool isIgnored = ignoredVersion == newVersion.version;

        if (newVersion.isNewerThan(currentVersion, currentBuildNumber) &&
            !isIgnored) {
          updateAvailable.value = true;

          // 如果显示加载对话框，先关闭它
          if (showLoading) Get.back();

          // 显示更新对话框
          _showUpdateDialog(newVersion);
        } else {
          updateAvailable.value = false;
          if (showLoading) {
            Get.back();
            Get.snackbar('检查更新', '您当前使用的已经是最新版本');
          }
        }
      } catch (error) {
        if (showLoading) {
          Get.back();
          Get.snackbar('检查更新失败', error.toString());
        }
        rethrow;
      }
    } catch (e) {
      if (showLoading) {
        Get.back();
        Get.snackbar('检查更新失败', '请检查网络连接: $e');
      }
    } finally {
      isCheckingUpdate.value = false;
    }
  }

  /// 使用原生方法检查更新
  Future<Map<String, dynamic>?> _checkNativeUpdate(
    String currentVersion,
  ) async {
    try {
      final result = await _updateChannel.invokeMethod<Map<dynamic, dynamic>>(
        'checkForUpdates',
        {
          'updateUrl': updateServerUrl,
          'backupUpdateUrl': backupUpdateServerUrl,
          'currentVersion': currentVersion
        },
      );

      if (result != null) {
        return Map<String, dynamic>.from(result);
      }

      return null;
    } catch (e) {
      print('原生更新检查失败: $e');
      Get.snackbar('原生更新检查失败', '将使用传统方式检查更新');
      return null;
    }
  }

  /// 显示更新对话框
  void _showUpdateDialog(VersionInfo version) {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.system_update, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('发现新版本'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('最新版本: ${version.version} (${version.buildNumber})'),
              if (version.releaseDate.isNotEmpty)
                Text('发布日期: ${version.releaseDate}'),
              const SizedBox(height: 16),
              const Text(
                '更新内容:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(version.releaseNotes, style: const TextStyle(height: 1.5)),
            ],
          ),
        ),
        actions: [
          if (!version.forceUpdate)
            TextButton(
              onPressed: () {
                // 忽略此版本
                _prefs.setString(_ignoreVersionKey, version.version);
                Get.back();
              },
              child: const Text('忽略此版本'),
            ),
          if (!version.forceUpdate)
            TextButton(onPressed: () => Get.back(), child: const Text('稍后更新')),
          ElevatedButton(
            onPressed: () =>
                downloadUpdate(version.downloadUrl, versionInfo: version),
            child: const Text('立即更新'),
          ),
        ],
      ),
      barrierDismissible: !version.forceUpdate,
    );
  }

  /// 下载更新
  Future<void> downloadUpdate(String url, {VersionInfo? versionInfo}) async {
    try {
      String downloadUrl;

      // 如果提供了VersionInfo对象，使用其platformSpecificUrl
      if (versionInfo != null) {
        downloadUrl = versionInfo.platformSpecificUrl;
        print('使用版本信息中的平台特定URL: $downloadUrl');
      } else {
        // 否则使用传入的URL
        downloadUrl = url;
        print('使用传入的URL: $downloadUrl');
      }

      // 如果是Windows平台，使用Windows更新服务
      if (!kIsWeb && Platform.isWindows) {
        // 打印日志，帮助调试
        print('Windows平台，使用应用内更新，URL: $downloadUrl');

        // 显示下载进度对话框
        Get.dialog(
          AlertDialog(
            title: const Text('正在下载更新'),
            content: Obx(() {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                      '下载进度: ${_windowsUpdateService.downloadProgress.value}%'),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: _windowsUpdateService.downloadProgress.value / 100,
                    minHeight: 10,
                  ),
                  if (_windowsUpdateService.downloadStatus.value == 'error')
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Text(
                        '下载失败，请稍后重试',
                        style: TextStyle(color: Colors.red[700]),
                      ),
                    ),
                ],
              );
            }),
            actions: [
              TextButton(
                onPressed: () {
                  _windowsUpdateService.cancelDownload();
                  Get.back();
                },
                child: const Text('取消'),
              ),
            ],
          ),
          barrierDismissible: false,
        );

        try {
          // 使用Windows更新服务下载并安装
          await _windowsUpdateService.downloadAndInstall(
            downloadUrl,
            onProgress: (progress) {
              // 更新进度
              downloadProgress.value = progress;
              // 打印日志，帮助调试
              if (progress % 10 == 0) {
                // 每10%打印一次，避免日志过多
                print('下载进度: $progress%');
              }
            },
            onComplete: () {
              // 下载完成，关闭进度对话框
              if (Get.isDialogOpen ?? false) {
                Get.back();
              }
              // 打印日志，帮助调试
              print('下载完成，准备安装');
            },
            onError: (error) {
              // 下载失败，显示错误信息
              if (Get.isDialogOpen ?? false) {
                Get.back();
              }
              print('下载失败: $error');
              Get.snackbar('更新失败', error.toString());
            },
          );
        } catch (e) {
          // 捕获可能的异常
          print('Windows更新服务异常: $e');
          if (Get.isDialogOpen ?? false) {
            Get.back();
          }
          Get.snackbar('更新失败', '下载或安装更新时出错: $e');
        }
      }
      // 如果是Android平台且启用了原生更新
      else if (Platform.isAndroid && _useNativeUpdate) {
        await _downloadNativeUpdate(downloadUrl);
      }
      // 其他平台使用传统方式打开浏览器下载
      else {
        final Uri uri = Uri.parse(downloadUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          Get.snackbar('更新失败', '无法打开下载链接');
        }
      }
    } catch (e) {
      Get.snackbar('更新失败', '打开下载链接时出错: $e');
    }
  }

  /// 使用原生方法下载更新
  Future<void> _downloadNativeUpdate(String url, {int retryCount = 0}) async {
    try {
      // 重置下载状态
      isDownloading.value = true;
      downloadProgress.value = 0;
      downloadStatus.value = 'downloading';

      // 获取当前版本信息
      final packageInfo = await PackageInfo.fromPlatform();

      print(
        '准备调用原生下载方法: url=$url, version=${packageInfo.version}, 重试次数=$retryCount',
      );

      // 确保事件通道已设置
      if (Platform.isAndroid) {
        // 每次下载前重新设置事件监听器
        _setupUpdateEventListener();

        // 添加延迟，确保通道已准备好
        await Future.delayed(const Duration(milliseconds: 500));
      }

      try {
        // 调用原生方法下载更新
        final Map<String, dynamic> params = {
          'downloadUrl': url,
          'version': packageInfo.version,
        };

        print('调用downloadUpdate方法，参数: $params');

        // 添加更长的延迟，确保通道已准备好
        if (retryCount > 0) {
          await Future.delayed(const Duration(seconds: 1));
        }

        // 尝试重新初始化通道
        if (retryCount > 0) {
          print('重新初始化通道');
          // 这里不能直接重新创建const变量，但可以通过临时变量调用
          final tempChannel = MethodChannel('com.daizhong.novelapp/update');
          final result = await tempChannel.invokeMethod<Map<dynamic, dynamic>>(
            'downloadUpdate',
            params,
          );

          print('使用临时通道调用结果: $result');

          if (result != null) {
            final status = result['status'];
            if (status == 'error') {
              isDownloading.value = false;
              Get.snackbar('下载失败', result['error'] ?? '未知错误');
            } else {
              print('下载已开始，状态: $status');
            }
            return;
          }
        } else {
          // 使用原始通道
          final result = await _updateChannel
              .invokeMethod<Map<dynamic, dynamic>>('downloadUpdate', params);

          print('原生下载方法调用结果: $result');

          if (result != null) {
            final status = result['status'];
            if (status == 'error') {
              isDownloading.value = false;
              Get.snackbar('下载失败', result['error'] ?? '未知错误');
            } else {
              print('下载已开始，状态: $status');
            }
            return;
          } else {
            print('原生下载方法返回null');
          }
        }
      } on PlatformException catch (e) {
        isDownloading.value = false;
        print('平台异常: ${e.code}, ${e.message}, ${e.details}');

        // 处理权限错误
        if (e.code == 'PERMISSION_REQUIRED') {
          // 请求权限
          _requestPermissions().then((granted) {
            if (granted) {
              // 权限已授予，重试下载
              print('权限已授予，重试下载');
              downloadUpdate(url);
            } else {
              // 权限仍未授予，显示提示
              Get.snackbar(
                '需要权限',
                '请授予应用所需的权限后重试',
                duration: const Duration(seconds: 5),
                mainButton: TextButton(
                  onPressed: () {
                    // 重试下载
                    downloadUpdate(url);
                  },
                  child:
                      const Text('重试', style: TextStyle(color: Colors.white)),
                ),
              );
            }
          });
        } else if (e.code == 'PERMISSION_DENIED') {
          Get.snackbar(
            '权限被拒绝',
            '您拒绝了所需的权限，更新功能将无法正常工作。请在系统设置中手动授予权限。',
            duration: const Duration(seconds: 5),
            mainButton: TextButton(
              onPressed: () {
                // 打开应用设置
                _openAppSettings();
              },
              child: const Text('去设置', style: TextStyle(color: Colors.white)),
            ),
          );
        } else {
          Get.snackbar('下载失败', '平台异常: ${e.message}');
        }
      } on MissingPluginException catch (e) {
        isDownloading.value = false;
        print('插件异常: $e');

        // 如果是插件异常，尝试重试几次
        if (retryCount < 3) {
          print('尝试重试下载，重试次数: ${retryCount + 1}');
          // 延迟更长时间后重试
          await Future.delayed(const Duration(seconds: 2));
          return _downloadNativeUpdate(url, retryCount: retryCount + 1);
        } else {
          // 如果多次重试后仍然失败，尝试使用传统方式下载
          print('多次重试后仍然失败，尝试使用传统方式下载');
          final Uri uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            Get.snackbar('下载提示', '已切换到浏览器下载模式');
          } else {
            Get.snackbar(
              '下载失败',
              '调用原生下载方法失败: 找不到插件实现。请尝试重新启动应用。',
              duration: const Duration(seconds: 5),
              mainButton: TextButton(
                onPressed: () {
                  // 重试下载
                  downloadUpdate(url);
                },
                child: const Text('重试', style: TextStyle(color: Colors.white)),
              ),
            );
          }
        }
      } catch (e) {
        isDownloading.value = false;
        print('调用原生下载方法异常: $e');

        // 如果是其他异常，也尝试重试几次
        if (retryCount < 3) {
          print('尝试重试下载，重试次数: ${retryCount + 1}');
          // 延迟一段时间后重试
          await Future.delayed(const Duration(seconds: 1));
          return _downloadNativeUpdate(url, retryCount: retryCount + 1);
        } else {
          Get.snackbar('下载失败', '调用原生下载方法失败: $e');
        }
      }
    } catch (e) {
      isDownloading.value = false;
      print('下载更新总体异常: $e');
      Get.snackbar('更新失败', '下载过程发生错误: $e');
    }
  }

  /// 取消下载
  Future<void> cancelDownload() async {
    if (!kIsWeb &&
        Platform.isAndroid &&
        _useNativeUpdate &&
        isDownloading.value) {
      try {
        await _updateChannel.invokeMethod('cancelDownload');
        isDownloading.value = false;
        downloadStatus.value = 'canceled';
        Get.snackbar('已取消', '已取消下载更新');
      } catch (e) {
        Get.snackbar('取消失败', '取消下载失败: $e');
      }
    }
  }

  /// 请求必要的权限
  Future<bool> _requestPermissions() async {
    if (kIsWeb || !Platform.isAndroid) return true;

    try {
      // 使用权限通道请求权限
      final result =
          await const MethodChannel('com.daizhong.novelapp/permissions')
              .invokeMethod<bool>('requestPermissions');

      return result ?? false;
    } catch (e) {
      print('请求权限失败: $e');
      return false;
    }
  }

  /// 打开应用设置页面
  Future<void> _openAppSettings() async {
    if (kIsWeb || !Platform.isAndroid) return;

    try {
      await openAppSettings();
    } catch (e) {
      print('打开应用设置失败: $e');
      Get.snackbar('操作失败', '无法打开应用设置页面');
    }
  }

  /// 判断是否应该检查更新
  bool _shouldCheckForUpdates() {
    final lastCheckTime = _prefs.getInt(_lastCheckTimeKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - lastCheckTime > _checkInterval.inMilliseconds;
  }

  /// 重置忽略的版本
  void resetIgnoredVersion() {
    _prefs.remove(_ignoreVersionKey);
  }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>岱宗文脉管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Noto Serif SC', serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
        }
        .sidebar .nav-link:hover {
            color: white;
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            padding: 20px;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div v-if="!isLoggedIn" class="login-container">
            <h2 class="text-center mb-4">岱宗文脉管理系统</h2>
            <div v-if="loginError" class="alert alert-danger">{{ loginError }}</div>
            <form @submit.prevent="login">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" v-model="loginForm.username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" v-model="loginForm.password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100" :disabled="isLoading">
                    <span v-if="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    登录
                </button>
            </form>
        </div>

        <!-- 主界面 -->
        <div v-else class="container-fluid">
            <div class="row">
                <!-- 侧边栏 -->
                <div class="col-md-2 sidebar p-0">
                    <div class="d-flex flex-column p-3">
                        <h5 class="mb-4 text-center">岱宗文脉管理系统</h5>
                        <ul class="nav nav-pills flex-column mb-auto">
                            <li class="nav-item">
                                <a href="#" class="nav-link" :class="{ active: currentPage === 'dashboard' }" @click="currentPage = 'dashboard'">
                                    <i class="bi bi-speedometer2 me-2"></i>仪表盘
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" :class="{ active: currentPage === 'announcements' }" @click="currentPage = 'announcements'">
                                    <i class="bi bi-megaphone me-2"></i>公告管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" :class="{ active: currentPage === 'users' }" @click="currentPage = 'users'">
                                    <i class="bi bi-people me-2"></i>用户管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" :class="{ active: currentPage === 'settings' }" @click="currentPage = 'settings'">
                                    <i class="bi bi-gear me-2"></i>系统设置
                                </a>
                            </li>
                        </ul>
                        <hr>
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-2"></i>
                                <strong>{{ currentUser.username }}</strong>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                                <li><a class="dropdown-item" href="#" @click="logout">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 主内容区 -->
                <div class="col-md-10 main-content">
                    <!-- 仪表盘 -->
                    <div v-if="currentPage === 'dashboard'">
                        <h2>仪表盘</h2>
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户总数</h5>
                                        <p class="card-text display-4">{{ stats.userCount || 0 }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">小说总数</h5>
                                        <p class="card-text display-4">{{ stats.novelCount || 0 }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">活跃公告</h5>
                                        <p class="card-text display-4">{{ stats.activeAnnouncementCount || 0 }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 公告管理 -->
                    <div v-if="currentPage === 'announcements'">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>公告管理</h2>
                            <button class="btn btn-primary" @click="showAnnouncementModal()">
                                <i class="bi bi-plus-circle me-2"></i>新增公告
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>重要</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="announcement in announcements" :key="announcement.id">
                                        <td>{{ announcement.id.substring(0, 8) }}...</td>
                                        <td>{{ announcement.title }}</td>
                                        <td>
                                            <span v-if="announcement.is_important" class="badge bg-danger">重要</span>
                                            <span v-else class="badge bg-secondary">普通</span>
                                        </td>
                                        <td>
                                            <span v-if="announcement.is_active" class="badge bg-success">活跃</span>
                                            <span v-else class="badge bg-secondary">禁用</span>
                                        </td>
                                        <td>{{ formatDate(announcement.created_at) }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info me-2" @click="showAnnouncementModal(announcement)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" @click="deleteAnnouncement(announcement.id)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 用户管理 -->
                    <div v-if="currentPage === 'users'">
                        <h2>用户管理</h2>
                        <div class="table-responsive mt-4">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>VIP状态</th>
                                        <th>管理员</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="user in users" :key="user.id">
                                        <td>{{ user.id }}</td>
                                        <td>{{ user.username }}</td>
                                        <td>{{ user.email }}</td>
                                        <td>
                                            <span v-if="user.is_vip" class="badge bg-warning text-dark">VIP</span>
                                            <span v-else class="badge bg-secondary">普通</span>
                                        </td>
                                        <td>
                                            <span v-if="user.is_admin" class="badge bg-primary">管理员</span>
                                            <span v-else class="badge bg-secondary">普通</span>
                                        </td>
                                        <td>
                                            <span v-if="user.is_active" class="badge bg-success">活跃</span>
                                            <span v-else class="badge bg-danger">禁用</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-info me-2" @click="showUserModal(user)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm" :class="user.is_active ? 'btn-warning' : 'btn-success'" 
                                                    @click="toggleUserStatus(user.id, !user.is_active)">
                                                <i :class="user.is_active ? 'bi bi-x-circle' : 'bi bi-check-circle'"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 系统设置 -->
                    <div v-if="currentPage === 'settings'">
                        <h2>系统设置</h2>
                        <div class="card mt-4">
                            <div class="card-body">
                                <h5 class="card-title">应用设置</h5>
                                <form @submit.prevent="saveSettings">
                                    <div class="mb-3">
                                        <label for="appName" class="form-label">应用名称</label>
                                        <input type="text" class="form-control" id="appName" v-model="settings.appName">
                                    </div>
                                    <div class="mb-3">
                                        <label for="appVersion" class="form-label">当前版本</label>
                                        <input type="text" class="form-control" id="appVersion" v-model="settings.appVersion">
                                    </div>
                                    <div class="mb-3">
                                        <label for="updateUrl" class="form-label">更新服务器URL</label>
                                        <input type="text" class="form-control" id="updateUrl" v-model="settings.updateUrl">
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 公告编辑模态框 -->
        <div class="modal fade" id="announcementModal" tabindex="-1" aria-labelledby="announcementModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="announcementModalLabel">{{ editingAnnouncement.id ? '编辑公告' : '新增公告' }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="announcementTitle" class="form-label">标题</label>
                                <input type="text" class="form-control" id="announcementTitle" v-model="editingAnnouncement.title" required>
                            </div>
                            <div class="mb-3">
                                <label for="announcementContent" class="form-label">内容</label>
                                <textarea class="form-control" id="announcementContent" rows="6" v-model="editingAnnouncement.content" required></textarea>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="isImportant" v-model="editingAnnouncement.is_important">
                                <label class="form-check-label" for="isImportant">标记为重要</label>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="isActive" v-model="editingAnnouncement.is_active">
                                <label class="form-check-label" for="isActive">立即激活</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveAnnouncement">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户编辑模态框 -->
        <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="userModalLabel">编辑用户</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" v-model="editingUser.username" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" v-model="editingUser.email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">密码 (留空表示不修改)</label>
                                <input type="password" class="form-control" id="password" v-model="editingUser.password">
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="isVip" v-model="editingUser.is_vip">
                                <label class="form-check-label" for="isVip">VIP用户</label>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="isAdmin" v-model="editingUser.is_admin">
                                <label class="form-check-label" for="isAdmin">管理员</label>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="isActive" v-model="editingUser.is_active">
                                <label class="form-check-label" for="isActive">账户激活</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveUser">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载中遮罩 -->
        <div class="loading" v-if="isLoading">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@3.2.36/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>

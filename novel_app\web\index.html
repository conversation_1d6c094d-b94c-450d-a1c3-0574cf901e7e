<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="岱宗文脉 - AI驱动的小说创作平台">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="岱宗文脉">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>岱宗文脉</title>
  <link rel="manifest" href="manifest.json">

  <!-- 检查是否是首次访问或直接访问应用 -->
  <script>
    // 检查URL参数是否包含direct=true
    const urlParams = new URLSearchParams(window.location.search);
    const directAccess = urlParams.get('direct');

    // 检查本地存储中是否有标记表示用户已经通过欢迎页面进入
    const hasVisitedBefore = localStorage.getItem('visited_before');

    // 如果不是直接访问且用户没有访问过，则重定向到欢迎页面
    if (!directAccess && !hasVisitedBefore) {
      window.location.href = 'welcome.html';
    }

    // 标记用户已访问
    localStorage.setItem('visited_before', 'true');
  </script>

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = '{{flutter_service_worker_version}}';
  </script>
  <style>
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }

    .loader {
      border: 16px solid #f3f3f3;
      border-radius: 50%;
      border-top: 16px solid #3498db;
      width: 120px;
      height: 120px;
      -webkit-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
    }

    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="loading">
    <div class="loader"></div>
  </div>
  <!-- 直接加载main.dart.js -->
  <script src="main.dart.js" type="application/javascript"></script>
</body>
</html>

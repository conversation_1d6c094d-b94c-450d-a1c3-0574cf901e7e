# 岱宗文脉后台管理系统

本文档提供了岱宗文脉后台管理系统的使用说明，包括安装、配置和使用方法。

## 1. 系统概述

岱宗文脉后台管理系统是一个基于FastAPI和Vue.js开发的Web应用，用于管理岱宗文脉小说生成器的后台数据，包括用户管理、公告管理和系统设置等功能。

## 2. 安装与配置

### 2.1 环境要求

- Python 3.9+
- PostgreSQL 12+
- Nginx
- 宝塔面板（可选，但推荐使用）

### 2.2 使用宝塔面板安装

1. 登录宝塔面板
2. 安装以下软件：
   - Nginx
   - Python项目管理器
   - PostgreSQL
   - Redis（可选）

3. 创建PostgreSQL数据库：
   - 数据库名：`novel`
   - 用户名：`novel_user`
   - 密码：设置一个安全的密码

4. 上传或克隆项目代码到服务器：
   - 目录：`/www/wwwroot/novel_app`

5. 配置环境变量：
   - 复制 `.env.example` 为 `.env`
   - 编辑 `.env` 文件，配置数据库连接和管理员账户

6. 创建Python项目：
   - 项目名称：`novel_app`
   - 路径：`/www/wwwroot/novel_app/backend`
   - Python版本：3.9+
   - 框架：FastAPI
   - 启动方式：python
   - 启动文件名：`run.py`
   - 端口：`8000`

7. 安装项目依赖：
   ```bash
   cd /www/wwwroot/novel_app/backend
   pip install -r requirements.txt
   ```

8. 初始化数据库：
   ```bash
   cd /www/wwwroot/novel_app/backend
   python init_db.py
   ```

9. 配置Nginx：
   - 添加站点：`admin.dzwm.xyz`（或您的域名）
   - 根目录：`/www/wwwroot/novel_app/backend/admin`
   - 添加反向代理：
     - 目标URL：`http://127.0.0.1:8000`

10. 启动服务：
    - 在Python项目管理器中启动项目
    - 或使用命令：`python run.py`

## 3. 系统功能

### 3.1 登录系统

1. 访问管理后台：`http://admin.dzwm.xyz`（或您配置的域名）
2. 使用管理员账户登录：
   - 用户名：在 `.env` 文件中配置的 `ADMIN_USERNAME`
   - 密码：在 `.env` 文件中配置的 `ADMIN_PASSWORD`

### 3.2 仪表盘

仪表盘显示系统的基本统计信息，包括：
- 用户总数
- 小说总数
- 活跃公告数
- VIP用户数
- 管理员用户数

### 3.3 公告管理

公告管理功能允许您：
- 查看所有公告
- 创建新公告
- 编辑现有公告
- 删除公告
- 设置公告的重要性和活跃状态

#### 创建公告

1. 点击【新增公告】按钮
2. 填写公告标题和内容
3. 设置公告是否重要
4. 设置公告是否立即激活
5. 点击【保存】按钮

#### 编辑公告

1. 在公告列表中点击编辑按钮
2. 修改公告信息
3. 点击【保存】按钮

#### 删除公告

1. 在公告列表中点击删除按钮
2. 确认删除操作

### 3.4 用户管理

用户管理功能允许您：
- 查看所有用户
- 编辑用户信息
- 设置用户的VIP状态
- 设置用户的管理员权限
- 启用或禁用用户账户

#### 编辑用户

1. 在用户列表中点击编辑按钮
2. 修改用户信息
3. 点击【保存】按钮

#### 启用/禁用用户

1. 在用户列表中点击启用/禁用按钮
2. 确认操作

### 3.5 系统设置

系统设置功能允许您配置应用的基本参数，包括：
- 应用名称
- 当前版本
- 更新服务器URL

## 4. API接口

### 4.1 认证接口

- `POST /api/v1/auth/login`：用户登录
- `POST /api/v1/auth/register`：用户注册
- `GET /api/v1/auth/me`：获取当前用户信息

### 4.2 用户接口

- `GET /api/v1/users`：获取用户列表（仅管理员）
- `GET /api/v1/users/{user_id}`：获取指定用户（仅管理员）
- `PUT /api/v1/users/{user_id}`：更新用户信息（仅管理员）
- `DELETE /api/v1/users/{user_id}`：删除用户（仅超级管理员）

### 4.3 公告接口

- `GET /api/v1/announcements`：获取公告列表
- `GET /api/v1/announcements/latest`：获取最新公告
- `GET /api/v1/announcements/{announcement_id}`：获取指定公告
- `POST /api/v1/announcements`：创建新公告（仅管理员）
- `PUT /api/v1/announcements/{announcement_id}`：更新公告（仅管理员）
- `DELETE /api/v1/announcements/{announcement_id}`：删除公告（仅管理员）

### 4.4 统计接口

- `GET /api/v1/stats`：获取系统统计数据（仅管理员）

### 4.5 设置接口

- `GET /api/v1/settings`：获取应用设置（仅管理员）
- `POST /api/v1/settings`：更新应用设置（仅管理员）

### 4.6 版本接口

- `GET /api/v1/version`：获取版本信息（公开接口）
- `POST /api/v1/version`：更新版本信息（仅管理员）

## 5. 安全性考虑

### 5.1 认证与授权

- 使用JWT（JSON Web Token）进行用户认证
- 基于角色的访问控制（RBAC）
- 管理员权限控制

### 5.2 数据安全

- 密码使用bcrypt算法加密存储
- HTTPS加密传输
- 输入验证和过滤

### 5.3 防护措施

- 防SQL注入：使用SQLAlchemy ORM
- 防XSS攻击：输入验证和输出编码
- 防CSRF攻击：使用安全的HTTP头部

## 6. 故障排除

### 6.1 常见问题

1. **无法登录管理后台**
   - 检查用户名和密码是否正确
   - 确认用户是否有管理员权限
   - 检查JWT密钥是否正确

2. **API请求返回401或403错误**
   - 检查认证令牌是否有效
   - 确认用户是否有足够的权限

3. **数据库连接错误**
   - 检查数据库连接字符串
   - 确认PostgreSQL服务是否运行
   - 检查数据库用户权限

### 6.2 日志查看

- 应用日志：`/www/wwwroot/novel_app/backend/logs`
- Nginx日志：`/www/wwwlogs/admin.dzwm.xyz.log`
- PostgreSQL日志：`/var/log/postgresql`

## 7. 维护与更新

### 7.1 备份数据

```bash
# 备份数据库
pg_dump -U novel_user novel > /backup/novel_$(date +%Y%m%d).sql

# 备份配置文件
cp /www/wwwroot/novel_app/backend/.env /backup/.env.$(date +%Y%m%d)
```

### 7.2 更新系统

```bash
# 进入项目目录
cd /www/wwwroot/novel_app

# 拉取最新代码
git pull

# 安装依赖
cd backend
pip install -r requirements.txt

# 应用数据库迁移
python -m alembic upgrade head

# 重启服务
supervisorctl restart novel_app
```

## 8. 联系与支持

如有任何问题或需要支持，请联系：

- 邮箱：<EMAIL>
- 网站：https://dzwm.xyz

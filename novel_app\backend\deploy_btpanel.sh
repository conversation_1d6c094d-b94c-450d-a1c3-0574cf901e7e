#!/bin/bash

# 岱宗文脉后台管理系统部署脚本
# 适用于宝塔面板环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
   print_error "此脚本必须以root用户运行"
   exit 1
fi

# 检查宝塔面板是否安装
if [ ! -f "/etc/init.d/bt" ]; then
    print_error "未检测到宝塔面板，请先安装宝塔面板"
    exit 1
fi

print_info "开始部署岱宗文脉后台管理系统..."

# 设置变量
INSTALL_DIR="/www/wwwroot/novel_app"
BACKEND_DIR="$INSTALL_DIR/backend"
ADMIN_DIR="$BACKEND_DIR/admin"
DB_NAME="novel"
DB_USER="novel_user"
DB_PASS=$(openssl rand -base64 12)
ADMIN_USER="admin"
ADMIN_PASS=$(openssl rand -base64 8)
ADMIN_EMAIL="<EMAIL>"
SECRET_KEY=$(openssl rand -base64 32)

# 创建安装目录
print_info "创建安装目录..."
mkdir -p $INSTALL_DIR
mkdir -p $BACKEND_DIR
mkdir -p $ADMIN_DIR

# 检查PostgreSQL是否安装
if ! command -v psql &> /dev/null; then
    print_warning "PostgreSQL未安装，尝试通过宝塔面板安装..."
    
    # 使用宝塔API安装PostgreSQL
    cd /www/server/panel && python3 /www/server/panel/tools.py package install postgresql
    
    if [ $? -ne 0 ]; then
        print_error "PostgreSQL安装失败，请手动安装"
        exit 1
    fi
    
    print_success "PostgreSQL安装成功"
fi

# 创建PostgreSQL数据库和用户
print_info "创建PostgreSQL数据库和用户..."
su - postgres -c "psql -c \"CREATE USER $DB_USER WITH PASSWORD '$DB_PASS';\""
su - postgres -c "psql -c \"CREATE DATABASE $DB_NAME OWNER $DB_USER;\""
su - postgres -c "psql -c \"GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;\""

if [ $? -ne 0 ]; then
    print_error "数据库创建失败"
    exit 1
fi

print_success "数据库创建成功"

# 复制项目文件
print_info "复制项目文件..."
cp -r ./* $BACKEND_DIR/

# 创建环境变量文件
print_info "创建环境变量文件..."
cat > $BACKEND_DIR/.env << EOF
# 数据库配置
DATABASE_URL=postgresql://$DB_USER:$DB_PASS@localhost/$DB_NAME

# JWT配置
SECRET_KEY=$SECRET_KEY
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 应用配置
ADMIN_EMAIL=$ADMIN_EMAIL
ADMIN_PASSWORD=$ADMIN_PASS
ADMIN_USERNAME=$ADMIN_USER
EOF

print_success "环境变量文件创建成功"

# 安装Python依赖
print_info "安装Python依赖..."
cd $BACKEND_DIR
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    print_error "Python依赖安装失败"
    exit 1
fi

print_success "Python依赖安装成功"

# 初始化数据库
print_info "初始化数据库..."
cd $BACKEND_DIR
mkdir -p alembic/versions
python3 init_db.py

if [ $? -ne 0 ]; then
    print_error "数据库初始化失败"
    exit 1
fi

print_success "数据库初始化成功"

# 创建Python项目
print_info "创建Python项目..."
cd /www/server/panel && python3 /www/server/panel/tools.py project_add novel_app $BACKEND_DIR Python 8000 run.py

if [ $? -ne 0 ]; then
    print_error "Python项目创建失败，请手动创建"
    print_warning "请在宝塔面板中创建Python项目：项目名称=novel_app，路径=$BACKEND_DIR，端口=8000，启动文件=run.py"
else
    print_success "Python项目创建成功"
fi

# 创建网站
print_info "创建网站..."
cd /www/server/panel && python3 /www/server/panel/tools.py site_add admin.dznovel.xyz $ADMIN_DIR

if [ $? -ne 0 ]; then
    print_error "网站创建失败，请手动创建"
    print_warning "请在宝塔面板中创建网站：域名=admin.dznovel.xyz，根目录=$ADMIN_DIR"
else
    print_success "网站创建成功"
    
    # 添加反向代理
    print_info "添加反向代理..."
    cd /www/server/panel && python3 /www/server/panel/tools.py site_proxy admin.dznovel.xyz "API" "http://127.0.0.1:8000"
    
    if [ $? -ne 0 ]; then
        print_error "反向代理添加失败，请手动添加"
        print_warning "请在宝塔面板中为网站admin.dznovel.xyz添加反向代理：目标URL=http://127.0.0.1:8000"
    else
        print_success "反向代理添加成功"
    fi
fi

# 创建API网站
print_info "创建API网站..."
cd /www/server/panel && python3 /www/server/panel/tools.py site_add api.dznovel.xyz $BACKEND_DIR

if [ $? -ne 0 ]; then
    print_error "API网站创建失败，请手动创建"
    print_warning "请在宝塔面板中创建网站：域名=api.dznovel.xyz，根目录=$BACKEND_DIR"
else
    print_success "API网站创建成功"
    
    # 添加反向代理
    print_info "添加反向代理..."
    cd /www/server/panel && python3 /www/server/panel/tools.py site_proxy api.dzwm.xyz "API" "http://127.0.0.1:8000"
    
    if [ $? -ne 0 ]; then
        print_error "反向代理添加失败，请手动添加"
        print_warning "请在宝塔面板中为网站api.dzwm.xyz添加反向代理：目标URL=http://127.0.0.1:8000"
    else
        print_success "反向代理添加成功"
    fi
fi

# 启动Python项目
print_info "启动Python项目..."
cd /www/server/panel && python3 /www/server/panel/tools.py project_start novel_app

if [ $? -ne 0 ]; then
    print_error "Python项目启动失败，请手动启动"
    print_warning "请在宝塔面板中启动Python项目：novel_app"
else
    print_success "Python项目启动成功"
fi

# 输出安装信息
echo ""
echo "=============================================="
echo "      岱宗文脉后台管理系统部署完成！"
echo "=============================================="
echo ""
echo "管理后台地址: http://admin.dznovel.xyz"
echo "API地址: http://api.dznovel.xyz"
echo ""
echo "数据库信息:"
echo "  数据库名: $DB_NAME"
echo "  用户名: $DB_USER"
echo "  密码: $DB_PASS"
echo ""
echo "管理员账户:"
echo "  用户名: $ADMIN_USER"
echo "  密码: $ADMIN_PASS"
echo "  邮箱: $ADMIN_EMAIL"
echo ""
echo "请保存好以上信息，并修改默认密码！"
echo ""
echo "如需更多帮助，请参考文档: $BACKEND_DIR/ADMIN_SYSTEM_README.md"
echo "=============================================="

import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class AnnouncementService extends GetxService {
  static const String _lastAnnouncementIdKey = 'last_announcement_id';
  static const String _offlineAnnouncementKey = 'offline_announcement';
  final announcement = Rxn<Announcement>();

  // 服务器API地址
  final String _apiUrl = '${ApiConfig.baseUrl}/api/v1/announcements';
  final String _backupApiUrl = '${ApiConfig.backupUrl}/api/v1/announcements';

  @override
  void onInit() {
    super.onInit();
    initAnnouncement();
  }

  Future<void> initAnnouncement() async {
    try {
      // 先尝试使用离线缓存的公告，避免网络请求阻塞应用启动
      final offlineAnnouncement = await _getOfflineAnnouncement();
      final prefs = await SharedPreferences.getInstance();
      final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);

      // 如果有离线公告且未读，先显示它
      if (offlineAnnouncement != null &&
          lastAnnouncementId != offlineAnnouncement.id) {
        announcement.value = offlineAnnouncement;
      }

      // 异步获取最新公告，不阻塞应用启动
      _fetchLatestAnnouncementAsync();
    } catch (e) {
      print('Error initializing announcement: $e');
    }
  }

  // 异步获取最新公告
  Future<void> _fetchLatestAnnouncementAsync() async {
    try {
      // 获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();

      if (latestAnnouncement != null) {
        // 获取上次显示的公告ID
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);

        // 如果没有显示过当前公告，或者公告ID不同，则显示公告
        if (lastAnnouncementId != latestAnnouncement.id) {
          // 保存最新公告到本地，用于离线显示
          _saveOfflineAnnouncement(latestAnnouncement);

          // 如果当前没有显示公告，则显示新公告
          if (announcement.value == null) {
            announcement.value = latestAnnouncement;
          }
        }
      }
    } catch (e) {
      print('Error fetching latest announcement async: $e');
    }
  }

  Future<Announcement?> _fetchLatestAnnouncement() async {
    try {
      // 创建请求头，确保不发送凭据
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      // 创建客户端并设置超时
      final client = http.Client();
      try {
        // 设置较短的超时时间，避免阻塞应用启动
        final timeout = Duration(seconds: ApiConfig.connectionTimeout);

        // 先尝试主域名
        try {
          final response = await client
              .get(
                Uri.parse('$_apiUrl/latest'),
                headers: headers,
              )
              .timeout(timeout);

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            final data = json.decode(response.body);
            return Announcement.fromJson(data);
          }
        } catch (e) {
          print('主域名获取公告失败，尝试备用地址: $e');

          // 如果主域名失败，尝试备用地址
          final response = await client
              .get(
                Uri.parse('$_backupApiUrl/latest'),
                headers: headers,
              )
              .timeout(timeout);

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            final data = json.decode(response.body);
            return Announcement.fromJson(data);
          } else {
            print('备用地址获取公告失败: ${response.statusCode}');
          }
        }

        return null;
      } finally {
        client.close(); // 确保关闭客户端
      }
    } catch (e) {
      print('Error fetching announcement: $e');
      return null;
    }
  }

  Future<void> _saveOfflineAnnouncement(Announcement announcement) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _offlineAnnouncementKey, json.encode(announcement.toJson()));
    } catch (e) {
      print('Error saving offline announcement: $e');
    }
  }

  Future<Announcement?> _getOfflineAnnouncement() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final announcementJson = prefs.getString(_offlineAnnouncementKey);

      if (announcementJson != null) {
        return Announcement.fromJson(json.decode(announcementJson));
      }
      return null;
    } catch (e) {
      print('Error getting offline announcement: $e');
      return null;
    }
  }

  Future<void> init() async {
    await initAnnouncement();
  }

  Future<void> markAnnouncementAsRead() async {
    try {
      if (announcement.value != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastAnnouncementIdKey, announcement.value!.id);
        print('Marked announcement as read: ${announcement.value!.id}');
        announcement.value = null;
      }
    } catch (e) {
      print('Error marking announcement as read: $e');
    }
  }

  // 手动刷新公告
  Future<void> refreshAnnouncement() async {
    try {
      final latestAnnouncement = await _fetchLatestAnnouncement();
      if (latestAnnouncement != null) {
        // 保存最新公告到本地，用于离线显示
        _saveOfflineAnnouncement(latestAnnouncement);

        // 获取上次显示的公告ID
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);

        // 如果没有显示过当前公告，或者公告ID不同，则显示公告
        if (lastAnnouncementId != latestAnnouncement.id) {
          announcement.value = latestAnnouncement;
          return;
        }
      }

      // 如果没有新公告，显示提示
      Get.snackbar('提示', '没有新的公告');
    } catch (e) {
      print('Error refreshing announcement: $e');
      Get.snackbar('错误', '刷新公告失败: $e');
    }
  }
}

class Announcement {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final bool isImportant;
  final bool isActive;

  Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.isImportant = false,
    this.isActive = true,
  });

  // 从JSON创建Announcement对象
  factory Announcement.fromJson(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: DateTime.parse(json['created_at'] as String),
      isImportant: json['is_important'] as bool,
      isActive: json['is_active'] as bool,
    );
  }

  // 将Announcement对象转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'created_at': date.toIso8601String(),
      'is_important': isImportant,
      'is_active': isActive,
    };
  }
}

#!/usr/bin/env python3
"""
下载CanvasKit文件到本地目录
"""
import os
import requests
import shutil
import sys

# CanvasKit版本
CANVASKIT_VERSION = "18b71d647a292a980abb405ac7d16fe1f0b20434"

# 文件列表
FILES = [
    f"https://www.gstatic.com/flutter-canvaskit/{CANVASKIT_VERSION}/canvaskit.js",
    f"https://www.gstatic.com/flutter-canvaskit/{CANVASKIT_VERSION}/canvaskit.wasm",
]

def download_file(url, dest_path):
    """下载文件到指定路径"""
    print(f"下载 {url} 到 {dest_path}")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(dest_path, 'wb') as f:
            shutil.copyfileobj(response.raw, f)
        
        print(f"成功下载 {dest_path}")
        return True
    except Exception as e:
        print(f"下载失败: {e}")
        return False

def main():
    """主函数"""
    # 创建canvaskit目录
    canvaskit_dir = os.path.join("canvaskit")
    os.makedirs(canvaskit_dir, exist_ok=True)
    
    # 下载文件
    success = True
    for file_url in FILES:
        filename = os.path.basename(file_url)
        dest_path = os.path.join(canvaskit_dir, filename)
        if not download_file(file_url, dest_path):
            success = False
    
    if success:
        print("\n所有文件下载成功！")
        print("请确保将这些文件部署到您的web服务器的canvaskit目录中。")
    else:
        print("\n部分文件下载失败，请检查网络连接并重试。")
        sys.exit(1)

if __name__ == "__main__":
    main()

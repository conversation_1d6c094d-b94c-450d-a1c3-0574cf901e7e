import 'package:langchain/langchain.dart';

/// 小说生成的提示词模板集合
class NovelPromptTemplates {
  /// 大纲生成提示模板 - 要求 JSON 数组输出 (支持分批)
  static final PromptTemplate outlineTemplate = PromptTemplate.fromTemplate('''
你是一位专业的中文小说策划师。请使用中文回复。
任务：根据以下提供的小说信息，为第 {startChapter} 章到第 {endChapter} 章（包含两端）创作小说大纲。

**重要提示：**
*   请严格按照指定的 JSON 格式输出，必须使用中文内容。
*   输出内容**仅包含一个 JSON 数组**，数组中包含从 `{startChapter}` 到 `{endChapter}` 的所有章节对象。
*   **不要包含任何 JSON 数组之外的文本、解释、注释或代码块标记 (```json ... ```)。**
*   **必须直接输出JSON数组，不要添加任何前言、解释或其他文字。**
*   为每一章提供明确的章节编号 (`chapterNumber`)、章节标题 (`chapterTitle`) 和详细的内容概要 (`summary`)。
*   确保 `chapterNumber` 是整数，`chapterTitle` 和 `summary` 是字符串，且内容必须是中文。

**小说信息:**
*   标题: {novelTitle}
*   类型: {genres}
*   主题: {theme}
*   目标读者: {targetReaders}
*   总章节数: {totalChapters}
*   故事背景: {background}
*   其他要求: {otherRequirements}
*   角色设定:
{characters}
*   写作风格参考:
{writingStylePrompt}
*   需融合的知识:
{knowledgeBase}

**输出 JSON 格式示例 (仅数组):**
[
  {{
    "chapterNumber": {startChapter},
    "chapterTitle": "第 {startChapter} 章的标题",
    "summary": "第 {startChapter} 章的详细内容概要..."
  }},
  // ... 中间章节 ...
  {{
    "chapterNumber": {endChapter},
    "chapterTitle": "第 {endChapter} 章的标题",
    "summary": "第 {endChapter} 章的详细内容概要..."
  }}
]

请现在仅生成从第 {startChapter} 章到第 {endChapter} 章的中文JSON数组内容，直接输出JSON数组，不要添加任何其他文字。
''');

  /// 章节细纲生成提示模板 - 针对单个章节
  static final PromptTemplate detailedOutlinePrompt =
      PromptTemplate.fromTemplate('''
你是一位专业的中文小说编辑和策划师。请使用中文回复。
任务：根据以下提供的小说整体设定和指定章节的初步信息，为**第 {chapterNumber} 章**生成详细的情节细纲。

**小说整体设定:**
*   标题: {novelTitle}
*   类型: {genres}
*   主题: {theme}
*   目标读者: {targetReaders}
*   故事背景: {background}
*   角色设定:
{characters}
*   文风参考:
{writingStylePrompt}
*   其他要求: {otherRequirements}
*   知识库信息:
{knowledgeBase}

**当前章节信息:**
*   章节编号: {chapterNumber}
*   章节标题: {chapterTitle}
*   章节概要 (来自整体大纲): {chapterSummary}

**详细细纲要求:**
请为**第 {chapterNumber} 章**生成详细的情节梗概，直接输出 Markdown 格式的文本，包含以下部分：

### 章节摘要
[此处填写本章核心内容的简要概述 (1-2句话)]

### 关键情节
- [按顺序描述本章发生的主要事件、冲突和转折点]
- [例如：角色A遇到了挑战B]
- [例如：情节发生转折，揭示了秘密C]

### 场景设定
- [描述本章涉及的主要场景和环境氛围]
- [例如：阴暗的森林深处，气氛紧张]

### 涉及角色
- [列出本章出现的关键角色及其主要互动或状态变化]
- [例如：主角 (探索)，反派 (阻挠)，新角色D (引入)]

### 视角
[指定本章的主要叙事视角，例如：主角第一人称 / 第三人称限制 (主角视角) / 第三人称上帝视角 等]


**输出要求:**
*   请**只输出**为第 {chapterNumber} 章生成的 Markdown 格式的详细细纲文本。
*   **不要包含**任何额外的解释、标题、章节编号或格式标记（如 ```markdown ... ```）。
*   确保内容详尽、逻辑清晰，能够指导后续的章节内容创作。

请现在开始为第 {chapterNumber} 章生成详细细纲文本。
''');

  /// 生成章节内容的提示模板
  static final PromptTemplate chapterTemplate = PromptTemplate.fromTemplate('''
你是一位专业的中文小说创作助手，请使用中文创作。请根据以下信息生成一个高质量的小说章节。

# 创作要求
- 小说标题：{novelTitle}
- 小说类型：{genres}
- 小说主题：{theme}
- 目标读者：{targetReaders}
- 当前章节：第{chapterNumber}章《{chapterTitle}》

# 核心创作指令
**你必须严格按照以下章节细纲来创作本章的详细内容。细纲中的情节要点和顺序必须得到体现。**
章节细纲：
{outlineContent}

# 故事背景
{background}

# 其他要求
{otherRequirements}

# 历史上下文
{history}

# 角色设定
{characters}

# 写作风格要求
{writingStylePrompt}

# 专业知识
{knowledgeBase}

# 创作指南
1. 请直接创作章节正文，无需添加章节标题或编号
2. 章节内容要贴合上方提供的核心创作指令中的章节细纲要求
3. **必须与前文保持严格的连续性和一致性**，认真阅读历史上下文
4. 确保本章内容是前文的自然延续，情节、人物行为和对话都要与前文衔接紧密
5. 保持人物性格、世界观设定的一致性，不要出现与前文矛盾的描述
6. **严格禁止重复前文的段落、句子或情节**，每章内容必须是全新的
7. **在创作前，请仔细分析历史上下文，确保不会重复已经描述过的场景或对话**
8. 文字要生动流畅，情节要引人入胜
9. 多运用细节描写和对话，少用总结性语言
10. 章节字数控制在2100-3000字之间，根据章节情节的复杂度和重要性适当调整具体字数
11. 如有对话，请使用"xxxx"某某说道的格式
12. 禁止使用小标题、分段标记或编号
13. 禁止添加作者注、编者按等内容
14. 如果本章是第一章，则建立良好的故事基础；如果是后续章节，则必须无缝衔接前文
15. **每次创作新内容前，请检查是否与历史内容重复，确保提供全新的情节发展**

请现在开始创作第{chapterNumber}章的内容。
''');

  /// 续写小说的提示模板
  static final PromptTemplate continueTemplate = PromptTemplate.fromTemplate('''
你是一位专业的中文小说创作助手，请使用中文创作。请基于已有内容续写小说。

# 创作要求
- 小说标题：{novelTitle}
- 小说类型：{genres}
- 目标读者：{targetReaders}
- 续写章节数：{chapterCount}
- 续写提示：{continuePrompt}

# 已有内容
{existingContent}

# 写作风格要求
{writingStylePrompt}

# 专业知识
{knowledgeBase}

# 创作指南
1. 请生成续写的章节大纲，包括章节标题和内容概要
2. 续写内容必须与已有内容保持一致性，包括人物、世界观和情节发展
3. 章节大纲格式如下：

第X章：章节标题
章节内容概要...

第X+1章：章节标题
章节内容概要...

4. 请为{chapterCount}个新章节提供完整大纲
5. 确保续写情节的连贯性和逻辑性，避免与已有内容产生矛盾

请现在开始创作续写章节的大纲。
''');

  /// 生成短篇小说的提示模板
  static final PromptTemplate shortNovelTemplate =
      PromptTemplate.fromTemplate('''
你是一位专业的中文短篇小说创作助手，请使用中文创作。请根据以下信息创作一篇完整的短篇小说。

# 创作要求
- 小说标题：{novelTitle}
- 小说类型：{genres}
- 主题内容：{theme}
- 目标读者：{targetReaders}
- 目标字数：约{wordCount}字
- 故事背景：{background}
- 其他要求：{otherRequirements}

# 角色设定
{characters}

# 写作风格要求
{writingStylePrompt}

# 专业知识
{knowledgeBase}

{history}

# 创作指南
1. 请直接创作短篇小说的完整内容，无需添加标题或章节编号
2. 小说需要包含完整的开端、发展、高潮和结局
3. 保持人物形象和情节的一致性
4. 文字要生动流畅，情节要引人入胜
5. 多运用细节描写和对话，少用总结性语言
6. 如有对话，请使用"xxxx"某某说道的格式
7. 禁止使用小标题、分段标记或编号
8. 禁止添加作者注、编者按等内容
9. 请确保小说字数接近{wordCount}字
10. 如果有前文内容，请确保新内容与前文无缝衔接，保持情节、人物和场景的连贯性

请现在开始创作这篇短篇小说。
''');

  /// 聊天对话的提示模板
  static final PromptTemplate chatTemplate = PromptTemplate.fromTemplate('''
你是一位专业的中文小说创作助手，请使用中文回复。正在与用户讨论小说《{novelTitle}》。

以下是小说的内容和大纲：
{novelContent}

请根据用户的问题或要求，提供专业、有帮助的回复。回复应该基于小说的内容和上下文，保持一致性和连贯性。
如果用户询问小说中的情节、角色或设定，请根据上述内容回答。
如果用户要求修改或扩展小说内容，请提供具体的建议和指导。
如果用户的问题与小说无关，请礼貌地将话题引导回小说创作。

用户消息: {userMessage}
''');
}

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import json
import os
from typing import Dict, Any

from ..database import get_db
from ..models import User
from ..auth import get_current_admin_user

router = APIRouter(
    prefix="/api/v1/settings",
    tags=["settings"],
    responses={404: {"description": "Not found"}},
)

# 设置文件路径
SETTINGS_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "settings.json")

# 默认设置
DEFAULT_SETTINGS = {
    "appName": "岱宗文脉",
    "appVersion": "4.2.5",
    "updateUrl": "http://*************/api/version.json"
}

def get_settings():
    """获取应用设置"""
    if os.path.exists(SETTINGS_FILE):
        try:
            with open(SETTINGS_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"读取设置文件失败: {e}")
            return DEFAULT_SETTINGS
    else:
        # 如果文件不存在，创建默认设置文件
        save_settings(DEFAULT_SETTINGS)
        return DEFAULT_SETTINGS

def save_settings(settings: Dict[str, Any]):
    """保存应用设置"""
    try:
        with open(SETTINGS_FILE, "w", encoding="utf-8") as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存设置文件失败: {e}")
        return False

@router.get("/")
async def read_settings(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取应用设置（仅管理员）"""
    return get_settings()

@router.post("/")
async def update_settings(
    settings: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """更新应用设置（仅管理员）"""
    # 获取当前设置
    current_settings = get_settings()
    
    # 更新设置
    for key, value in settings.items():
        current_settings[key] = value
    
    # 保存设置
    if save_settings(current_settings):
        return {"status": "success", "message": "设置已更新"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存设置失败"
        )

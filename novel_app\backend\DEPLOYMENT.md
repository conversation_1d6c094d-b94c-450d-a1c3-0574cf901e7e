# 岱宗文脉后台管理系统部署指南 (CentOS)

本文档提供了在CentOS服务器上部署岱宗文脉后台管理系统的详细步骤。

## 系统要求

- CentOS 7/8
- Python 3.9+
- PostgreSQL 12+
- Nginx (用于反向代理)
- 具有SSH访问权限的服务器

## 1. 准备服务器环境

### 安装必要的软件包

```bash
# 更新系统包
sudo yum update -y

# 安装EPEL仓库
sudo yum install -y epel-release

# 安装开发工具
sudo yum groupinstall -y "Development Tools"

# 安装Python 3.9 (CentOS 7默认没有Python 3.9)
# 首先安装IUS仓库
sudo yum install -y https://repo.ius.io/ius-release-el7.rpm

# 安装Python 3.9和相关工具
sudo yum install -y python39 python39-devel python39-pip python39-setuptools

# 创建Python 3.9的软链接
sudo ln -sf /usr/bin/python3.9 /usr/bin/python3
sudo ln -sf /usr/bin/pip3.9 /usr/bin/pip3

# 安装PostgreSQL仓库
sudo yum install -y https://download.postgresql.org/pub/repos/yum/reporpms/EL-7-x86_64/pgdg-redhat-repo-latest.noarch.rpm

# 安装PostgreSQL 12
sudo yum install -y postgresql12 postgresql12-server postgresql12-contrib

# 初始化PostgreSQL数据库
sudo /usr/pgsql-12/bin/postgresql-12-setup initdb

# 启动并启用PostgreSQL服务
sudo systemctl start postgresql-12
sudo systemctl enable postgresql-12

# 安装Nginx
sudo yum install -y nginx

# 启动并启用Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 安装其他依赖
sudo yum install -y openssl-devel libffi-devel
```

### 创建PostgreSQL数据库

```bash
# 切换到postgres用户
sudo -u postgres psql

# 在PostgreSQL中执行以下命令
CREATE DATABASE novel;
CREATE USER novel_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE novel TO novel_user;
\q
```

### 配置PostgreSQL允许密码认证

编辑PostgreSQL配置文件：

```bash
sudo vi /var/lib/pgsql/12/data/pg_hba.conf
```

找到以下行：

```
# IPv4 local connections:
host    all             all             127.0.0.1/32            ident
```

将其修改为：

```
# IPv4 local connections:
host    all             all             127.0.0.1/32            md5
```

重启PostgreSQL服务：

```bash
sudo systemctl restart postgresql-12
```

## 2. 部署后端API

### 克隆代码库

```bash
# 创建应用目录
mkdir -p /var/www/novel_app
cd /var/www/novel_app

# 克隆代码库（如果使用Git）
git clone https://github.com/yourusername/novel_app.git .
# 或者上传项目文件到此目录
```

### 设置Python虚拟环境

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r backend/requirements.txt

# 如果遇到编译错误，可能需要安装额外的开发库
# sudo yum install -y postgresql-devel
```

### 配置环境变量

```bash
# 复制示例环境变量文件
cp backend/.env.example backend/.env

# 编辑环境变量文件
nano backend/.env
```

修改以下配置：

```
# 数据库配置
DATABASE_URL=postgresql://novel_user:your_secure_password@localhost/novel

# JWT配置
SECRET_KEY=your_random_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password
ADMIN_USERNAME=admin
```

### 初始化数据库

```bash
# 确保在虚拟环境中
cd backend

# 创建迁移目录
mkdir -p alembic/versions

# 初始化数据库表
alembic upgrade head

# 创建初始管理员用户
python init_db.py
```

### 设置Systemd服务

创建服务文件：

```bash
sudo vi /etc/systemd/system/novel-app.service
```

添加以下内容：

```ini
[Unit]
Description=Novel App Backend API
After=network.target

[Service]
User=nginx
Group=nginx
WorkingDirectory=/var/www/novel_app/backend
Environment="PATH=/var/www/novel_app/venv/bin"
ExecStart=/var/www/novel_app/venv/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

设置目录权限：

```bash
# 创建nginx用户（如果不存在）
id -u nginx &>/dev/null || sudo useradd -r nginx

# 设置目录权限
sudo chown -R nginx:nginx /var/www/novel_app
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl start novel-app
sudo systemctl enable novel-app
sudo systemctl status novel-app
```

## 3. 配置Nginx反向代理

创建Nginx配置文件：

```bash
sudo mkdir -p /etc/nginx/conf.d
sudo vi /etc/nginx/conf.d/novel-app.conf
```

添加以下内容：

```nginx
server {
    listen 80;
    server_name api.dzwm.xyz;  # 替换为您的域名

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

测试并重启Nginx：

```bash
sudo nginx -t  # 测试配置
sudo systemctl restart nginx
```

## 4. 设置SSL证书（推荐）

使用Let's Encrypt获取免费SSL证书：

```bash
# 安装certbot
sudo yum install -y certbot python3-certbot-nginx

# 获取并安装证书
sudo certbot --nginx -d api.dzwm.xyz
```

按照提示完成SSL证书的设置。

如果遇到问题，可以手动配置：

```bash
# 获取证书
sudo certbot certonly --standalone -d api.dzwm.xyz

# 编辑Nginx配置
sudo vi /etc/nginx/conf.d/novel-app.conf
```

添加SSL配置：

```nginx
server {
    listen 80;
    server_name api.dzwm.xyz;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name api.dzwm.xyz;

    ssl_certificate /etc/letsencrypt/live/api.dzwm.xyz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.dzwm.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 5. 防火墙设置

CentOS使用firewalld作为默认防火墙：

```bash
# 安装firewalld（如果尚未安装）
sudo yum install -y firewalld

# 启动并启用firewalld
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 允许SSH、HTTP和HTTPS
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https

# 应用更改
sudo firewall-cmd --reload

# 检查防火墙状态
sudo firewall-cmd --list-all
```

如果您使用的是云服务器（如阿里云、腾讯云等），还需要在云控制台的安全组中开放相应端口。

## 6. 维护和更新

### 查看日志

```bash
# 查看应用日志
sudo journalctl -u novel-app

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 更新应用

```bash
cd /var/www/novel_app

# 拉取最新代码（如果使用Git）
git pull

# 激活虚拟环境
source venv/bin/activate

# 安装新依赖
pip install -r backend/requirements.txt

# 应用数据库迁移
cd backend
alembic upgrade head

# 更新管理员用户
python init_db.py

# 重启服务
sudo systemctl restart novel-app
```

## 7. 备份数据库

创建定期备份脚本：

```bash
sudo vi /usr/local/bin/backup-novel-db.sh
```

添加以下内容：

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/novel"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/novel_$TIMESTAMP.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库 (使用PostgreSQL 12的路径)
/usr/pgsql-12/bin/pg_dump -U novel_user novel > $BACKUP_FILE

# 压缩备份
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -type f -mtime +7 -delete
```

设置权限并添加到crontab：

```bash
sudo chmod +x /usr/local/bin/backup-novel-db.sh
sudo crontab -e
```

添加以下行以每天凌晨3点进行备份：

```
0 3 * * * /usr/local/bin/backup-novel-db.sh
```

如果遇到权限问题，可能需要修改PostgreSQL配置允许本地备份：

```bash
# 编辑PostgreSQL配置
sudo vi /var/lib/pgsql/12/data/pg_hba.conf

# 添加以下行
local   all             novel_user                                 md5

# 重启PostgreSQL
sudo systemctl restart postgresql-12
```

## 故障排除

### 常见问题

1. **服务无法启动**：
   ```bash
   # 检查应用日志
   sudo journalctl -u novel-app

   # 检查服务状态
   sudo systemctl status novel-app
   ```

2. **数据库连接错误**：
   ```bash
   # 确认PostgreSQL服务正在运行
   sudo systemctl status postgresql-12

   # 检查数据库连接配置
   sudo vi backend/.env

   # 测试数据库连接
   sudo -u nginx psql -U novel_user -h localhost -d novel_app
   ```

3. **Nginx 502 错误**：
   ```bash
   # 检查应用是否正在运行
   sudo systemctl status novel-app

   # 检查Nginx配置
   sudo nginx -t

   # 检查Nginx错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

4. **SELinux问题**：
   CentOS默认启用SELinux，可能会阻止Nginx代理到后端应用：
   ```bash
   # 检查SELinux状态
   getenforce

   # 临时设置SELinux为宽容模式
   sudo setenforce 0

   # 永久设置SELinux为宽容模式
   sudo sed -i 's/SELINUX=enforcing/SELINUX=permissive/' /etc/selinux/config

   # 或者配置SELinux允许Nginx代理
   sudo setsebool -P httpd_can_network_connect 1
   ```

5. **防火墙问题**：
   ```bash
   # 检查防火墙状态
   sudo firewall-cmd --state

   # 临时关闭防火墙进行测试
   sudo systemctl stop firewalld

   # 如果关闭防火墙后问题解决，则添加正确的规则
   sudo firewall-cmd --permanent --add-port=8000/tcp
   sudo firewall-cmd --reload
   ```

如有其他问题，请查看应用日志和系统日志以获取更多信息。

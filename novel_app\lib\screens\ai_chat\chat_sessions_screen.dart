import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/chat_session.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/screens/ai_chat/daizong_ai_screen.dart';
import 'package:intl/intl.dart';

/// 聊天会话列表页面
class ChatSessionsScreen extends StatefulWidget {
  const ChatSessionsScreen({Key? key}) : super(key: key);

  @override
  State<ChatSessionsScreen> createState() => _ChatSessionsScreenState();
}

class _ChatSessionsScreenState extends State<ChatSessionsScreen> {
  final ChatHistoryService _chatHistoryService = Get.find<ChatHistoryService>();
  final TextEditingController _sessionNameController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    // 加载会话列表
    _loadSessions();
  }
  
  @override
  void dispose() {
    _sessionNameController.dispose();
    super.dispose();
  }
  
  /// 加载会话列表
  void _loadSessions() {
    // 会话列表已经在ChatHistoryService中加载，这里只需要刷新UI
    setState(() {});
  }
  
  /// 创建新会话
  Future<void> _createNewSession() async {
    // 显示对话框，让用户输入会话名称
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建新会话'),
        content: TextField(
          controller: _sessionNameController,
          decoration: const InputDecoration(
            hintText: '请输入会话名称',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              final name = _sessionNameController.text.trim();
              if (name.isNotEmpty) {
                await _chatHistoryService.createNormalChatSession(name);
                _sessionNameController.clear();
                Navigator.pop(context);
                
                // 刷新会话列表
                _loadSessions();
                
                // 返回到聊天页面
                Get.back();
              }
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }
  
  /// 删除会话
  Future<void> _deleteSession(ChatSession session) async {
    // 显示确认对话框
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除会话'),
        content: Text('确定要删除会话"${session.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              await _chatHistoryService.deleteSession(session.id);
              Navigator.pop(context);
              
              // 刷新会话列表
              _loadSessions();
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('聊天会话'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSessions,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Obx(() {
        final sessions = _chatHistoryService.sessions;
        
        if (sessions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('暂无聊天会话'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _createNewSession,
                  child: const Text('创建新会话'),
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          itemCount: sessions.length,
          itemBuilder: (context, index) {
            final session = sessions[index];
            return _buildSessionItem(session);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewSession,
        tooltip: '创建新会话',
        child: const Icon(Icons.add),
      ),
    );
  }
  
  /// 构建会话列表项
  Widget _buildSessionItem(ChatSession session) {
    // 格式化时间
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = dateFormat.format(session.lastUpdatedAt);
    
    // 会话类型图标
    final icon = session.type == ChatSessionType.normal
        ? Icons.chat_bubble_outline
        : Icons.book_outlined;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(session.title),
        subtitle: Text(
          session.summary ?? '${formattedDate}',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () => _deleteSession(session),
          tooltip: '删除',
        ),
        onTap: () {
          // 返回到聊天页面，并加载选中的会话
          Get.back(result: session);
        },
      ),
    );
  }
}

import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'novel.g.dart';

// 小说类型枚举
enum NovelType {
  longNovel('长篇小说'),
  shortNovel('短篇小说');

  const NovelType(this.displayName);
  final String displayName;
}

// 短篇小说字数选项
enum ShortNovelWordCount {
  words3000(3000, '3000字'),
  words5000(5000, '5000字'),
  words8000(8000, '8000字'),
  words10000(10000, '10000字'),
  words15000(15000, '15000字'),
  words20000(20000, '20000字');

  const ShortNovelWordCount(this.count, this.displayName);
  final int count;
  final String displayName;
}

// 短篇小说大纲部分
class ShortNovelOutlinePart {
  final int partNumber;
  final String title;
  final String description;
  final int startPercentage;
  final int endPercentage;
  final String detailedOutline; // 细纲内容

  ShortNovelOutlinePart({
    required this.partNumber,
    required this.title,
    required this.description,
    required this.startPercentage,
    required this.endPercentage,
    this.detailedOutline = '',
  });

  Map<String, dynamic> toJson() => {
        'partNumber': partNumber,
        'title': title,
        'description': description,
        'startPercentage': startPercentage,
        'endPercentage': endPercentage,
        'detailedOutline': detailedOutline,
      };

  factory ShortNovelOutlinePart.fromJson(Map<String, dynamic> json) =>
      ShortNovelOutlinePart(
        partNumber: json['partNumber'] as int,
        title: json['title'] as String,
        description: json['description'] as String,
        startPercentage: json['startPercentage'] as int,
        endPercentage: json['endPercentage'] as int,
        detailedOutline: json['detailedOutline'] as String? ?? '',
      );

  ShortNovelOutlinePart copyWith({
    int? partNumber,
    String? title,
    String? description,
    int? startPercentage,
    int? endPercentage,
    String? detailedOutline,
  }) {
    return ShortNovelOutlinePart(
      partNumber: partNumber ?? this.partNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      startPercentage: startPercentage ?? this.startPercentage,
      endPercentage: endPercentage ?? this.endPercentage,
      detailedOutline: detailedOutline ?? this.detailedOutline,
    );
  }
}

// 短篇小说大纲
class ShortNovelOutline {
  final String title;
  final List<ShortNovelOutlinePart> parts;
  final int totalWordCount;
  final int totalParts;

  ShortNovelOutline({
    required this.title,
    required this.parts,
    required this.totalWordCount,
    required this.totalParts,
  });

  Map<String, dynamic> toJson() => {
        'title': title,
        'parts': parts.map((part) => part.toJson()).toList(),
        'totalWordCount': totalWordCount,
        'totalParts': totalParts,
      };

  factory ShortNovelOutline.fromJson(Map<String, dynamic> json) =>
      ShortNovelOutline(
        title: json['title'] as String,
        parts: (json['parts'] as List)
            .map((part) =>
                ShortNovelOutlinePart.fromJson(part as Map<String, dynamic>))
            .toList(),
        totalWordCount: json['totalWordCount'] as int,
        totalParts: json['totalParts'] as int,
      );

  ShortNovelOutline copyWith({
    String? title,
    List<ShortNovelOutlinePart>? parts,
    int? totalWordCount,
    int? totalParts,
  }) {
    return ShortNovelOutline(
      title: title ?? this.title,
      parts: parts ?? this.parts,
      totalWordCount: totalWordCount ?? this.totalWordCount,
      totalParts: totalParts ?? this.totalParts,
    );
  }
}

@HiveType(typeId: 0)
class Novel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  final String genre;

  @HiveField(3)
  String outline;

  @HiveField(4)
  String content;

  @HiveField(5)
  final List<Chapter> chapters;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime? updatedAt;

  @HiveField(8)
  final String? style;

  @HiveField(9)
  final String? sessionId;

  String get createTime => createdAt.toString().split('.')[0];

  int get wordCount => content.replaceAll(RegExp(r'\s'), '').length;

  Novel({
    String? id,
    required this.title,
    required this.genre,
    required this.outline,
    required this.content,
    required this.chapters,
    required this.createdAt,
    this.updatedAt,
    this.style,
    this.sessionId,
  }) : id = id ?? const Uuid().v4();

  Novel copyWith({
    String? title,
    String? genre,
    String? outline,
    String? content,
    List<Chapter>? chapters,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? style,
    String? id,
    String? sessionId,
  }) {
    return Novel(
      title: title ?? this.title,
      genre: genre ?? this.genre,
      outline: outline ?? this.outline,
      content: content ?? this.content,
      chapters: chapters ?? this.chapters,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      style: style ?? this.style,
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'genre': genre,
        'outline': outline,
        'content': content,
        'chapters': chapters.map((c) => c.toJson()).toList(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'style': style,
        'sessionId': sessionId,
      };

  factory Novel.fromJson(Map<String, dynamic> json) => Novel(
        id: json['id'] as String,
        title: json['title'] as String,
        genre: json['genre'] as String,
        outline: json['outline'] as String,
        content: json['content'] as String,
        chapters: (json['chapters'] as List)
            .map((c) => Chapter.fromJson(c as Map<String, dynamic>))
            .toList(),
        createdAt: DateTime.parse(json['createdAt'] as String),
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'] as String)
            : null,
        style: json['style'] as String?,
        sessionId: json['sessionId'] as String?,
      );

  Chapter get outlineChapter {
    // 不再从章节列表中查找第0章
    // 直接返回包含大纲内容的临时章节对象
    return Chapter(
      number: 0,
      title: '大纲',
      content: outline,
    );
  }

  void addOutlineAsChapter() {
    // 此功能已弃用，不再将大纲保存为第0章
    // 为保持兼容性，保留此方法但不执行任何操作
    /*
    if (!chapters.any((chapter) => chapter.number == 0)) {
      chapters.insert(0, Chapter(
        number: 0,
        title: '大纲',
        content: outline,
      ));
    }
    */
  }
}

@HiveType(typeId: 1)
class Chapter {
  @HiveField(0)
  final int number;

  @HiveField(1)
  final String title;

  @HiveField(2)
  String content;

  int get index => number - 1;

  Chapter({
    required this.number,
    required this.title,
    required this.content,
  });

  Chapter copyWith({
    int? number,
    String? title,
    String? content,
  }) {
    return Chapter(
      number: number ?? this.number,
      title: title ?? this.title,
      content: content ?? this.content,
    );
  }

  Map<String, dynamic> toJson() => {
        'number': number,
        'title': title,
        'content': content,
      };

  factory Chapter.fromJson(Map<String, dynamic> json) => Chapter(
        number: json['number'] as int,
        title: json['title'] as String,
        content: json['content'] as String,
      );
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:novel_app/theme/app_theme.dart';

class ThemeController extends GetxController {
  static const String _keyThemeMode = 'theme_mode';

  final _prefs = Get.find<SharedPreferences>();
  final RxBool _isDarkMode = false.obs;

  bool get isDarkMode => _isDarkMode.value;

  @override
  void onInit() {
    super.onInit();
    _loadThemeSettings();
    // 应用初始主题
    Get.changeTheme(
        _isDarkMode.value ? AppTheme.darkTheme : AppTheme.lightTheme);
  }

  void _loadThemeSettings() {
    _isDarkMode.value = _prefs.getBool(_keyThemeMode) ?? false;
  }

  void toggleTheme() {
    _isDarkMode.value = !_isDarkMode.value;
    _prefs.setBool(_keyThemeMode, _isDarkMode.value);
    Get.changeTheme(
        _isDarkMode.value ? AppTheme.darkTheme : AppTheme.lightTheme);
  }

  // 获取调整后的背景色
  Color getAdjustedBackgroundColor() {
    return Get.theme.scaffoldBackgroundColor;
  }
}

﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\flutter\x64\Release\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\file_selector_windows\Release\file_selector_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\just_audio_windows\Release\just_audio_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\share_plus\Release\share_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Release\novel_app.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Release\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>